# 主机入侵攻击Controller开发任务

## 任务概述
创建 FfsafeHostIntrusionAttackController 类，完全仿照 FfsafeFlowRiskAssetsController 的结构和接口数量，操作 `ffsafe_host_intrusion_attack` 表，并支持指定的查询条件。

## 任务背景
- **目标表**：`ffsafe_host_intrusion_attack` 和 `ffsafe_host_intrusion_attack_detail`
- **参考模板**：`FfsafeFlowRiskAssetsController`
- **架构模式**：Controller -> Service -> Mapper 三层架构

## 已完成的工作

### ✅ 阶段1：创建查询DTO类
**文件**：`FfsafeHostIntrusionAttackQueryDto.java`
- 继承 BaseEntity（包含分页参数）
- 支持查询条件：
  - 攻击源IP（sip）- 精确匹配
  - 目标IP（dip）- 精确匹配
  - 目标主机名（dipName）- 模糊查询
  - 告警名称（alertName）- 模糊查询
  - 处置状态（handleState）- 精确匹配
  - 所属探针（deviceConfigId）- 精确匹配
  - 处置人（disposer）- 模糊查询
  - 非凡ID（ffId）- 精确匹配
  - 时间范围查询（通过 params.beginTime 和 params.endTime）

### ✅ 阶段2：创建处置DTO类
**文件**：`FfsafeHostIntrusionAttackHandleDto.java`
- 包含字段：id、handleState、handleDesc
- 添加 JSR-303 验证注解：@NotNull、@Size
- 使用 Swagger 注解进行API文档说明

### ✅ 阶段3：创建Controller类
**文件**：`FfsafeHostIntrusionAttackController.java`
- 完全仿照 FfsafeFlowRiskAssetsController 的所有接口
- 包含接口：
  1. `GET /list` - 分页查询列表
  2. `POST /export` - 导出Excel
  3. `GET /{id}` - 获取详细信息
  4. `PUT /` - 修改攻击事件
  5. `DELETE /{id}` - 删除单个攻击事件
  6. `DELETE /batch/{ids}` - 批量删除
  7. `GET /statistics` - 统计查询
  8. `POST /handle` - 单个处置
  9. `POST /batchHandle` - 批量处置
  10. `GET /detail/{attackId}` - 根据attackId查询详情（新增）

### ✅ 阶段4：扩展Service接口
**文件**：`IFfsafeHostIntrusionAttackService.java`
- 添加新方法：
  - `selectFfsafeHostIntrusionAttackListByQuery(FfsafeHostIntrusionAttackQueryDto queryDto)`
  - `selectAttackTypeStatistics(FfsafeHostIntrusionAttackQueryDto queryDto)`
  - `selectAttackDetailByAttackId(Long attackId)`

### ✅ 阶段5：扩展Service实现类
**文件**：`FfsafeHostIntrusionAttackServiceImpl.java`
- 实现新增的Service接口方法
- 添加对 IFfsafeHostIntrusionAttackDetailService 的依赖注入

### ✅ 阶段6：扩展Mapper接口
**文件**：`FfsafeHostIntrusionAttackMapper.java`
- 添加新方法：
  - `selectFfsafeHostIntrusionAttackListByQuery(FfsafeHostIntrusionAttackQueryDto queryDto)`
  - `selectAttackTypeStatistics(FfsafeHostIntrusionAttackQueryDto queryDto)`
  - `selectAttackDetailByAttackId(@Param("attackId") Long attackId)`

### ✅ 阶段7：更新Mapper XML文件
**文件**：`FfsafeHostIntrusionAttackMapper.xml`
- 添加查询SQL：
  - `selectFfsafeHostIntrusionAttackListByQuery` - 支持多条件查询和时间范围
  - `selectAttackTypeStatistics` - 统计各处置状态数量、唯一源IP、唯一目标IP
  - `selectAttackDetailByAttackId` - 关联查询主表和详情表

## 技术实现要点

### 查询条件支持
- **精确匹配**：sip、dip、handleState、deviceConfigId、ffId
- **模糊查询**：dipName、alertName、disposer
- **时间范围**：通过 params.beginTime 和 params.endTime
- **分页支持**：继承 BaseEntity 的分页参数

### 统计功能
- 总数统计
- 各处置状态统计（未处置、已处置、忽略）
- 唯一攻击源IP数量
- 唯一目标IP数量

### 详情查询
- 主表信息：基本攻击事件信息
- 详情表信息：JSON格式的详细攻击数据
- 左连接查询，支持无详情数据的攻击事件

## 接口对比

| 功能 | FfsafeFlowRiskAssetsController | FfsafeHostIntrusionAttackController |
|------|-------------------------------|-------------------------------------|
| 分页查询 | ✅ | ✅ |
| 导出Excel | ✅ | ✅ |
| 获取详情 | ✅ | ✅ |
| 修改 | ✅ | ✅ |
| 删除单个 | ✅ | ✅ |
| 批量删除 | ✅ | ✅ |
| 统计查询 | ✅ | ✅ |
| 单个处置 | ✅ | ✅ |
| 批量处置 | ✅ | ✅ |
| attackId详情 | ❌ | ✅（新增） |

## 验证要点

1. **编译验证**：所有类无编译错误
2. **接口完整性**：与参考Controller接口数量一致
3. **查询条件**：支持所有指定的查询条件
4. **分页功能**：正确继承BaseEntity的分页支持
5. **参数验证**：DTO类包含适当的验证注解
6. **SQL正确性**：XML中的SQL语句语法正确
7. **关联查询**：attackId详情查询能正确关联两个表

## 项目结构
```
aqsoc-monitor/src/main/java/com/ruoyi/ffsafe/api/
├── controller/
│   └── FfsafeHostIntrusionAttackController.java
├── domain/
│   ├── FfsafeHostIntrusionAttackQueryDto.java
│   └── FfsafeHostIntrusionAttackHandleDto.java
├── service/
│   ├── IFfsafeHostIntrusionAttackService.java
│   └── impl/
│       └── FfsafeHostIntrusionAttackServiceImpl.java
└── mapper/
    └── FfsafeHostIntrusionAttackMapper.java

aqsoc-monitor/src/main/resources/mapper/ffsafe/
└── FfsafeHostIntrusionAttackMapper.xml
```

## 任务状态
**状态**：✅ 已完成
**完成时间**：2025-08-26
**验证状态**：编译通过，无语法错误
