package com.ruoyi.ffsafe.api.controller;

import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.ffsafe.api.domain.FfsafeHostIntrusionAttack;
import com.ruoyi.ffsafe.api.domain.FfsafeHostIntrusionAttackQueryDto;
import com.ruoyi.ffsafe.api.domain.FfsafeHostIntrusionAttackHandleDto;
import com.ruoyi.ffsafe.api.service.IFfsafeHostIntrusionAttackService;
import javax.validation.Valid;

/**
 * 主机入侵攻击Controller
 * 
 * <AUTHOR>
 * @date 2025-08-26
 */
@RestController
@RequestMapping("/ffsafe/hostIntrusionAttack")
public class FfsafeHostIntrusionAttackController extends BaseController {

    @Autowired
    private IFfsafeHostIntrusionAttackService ffsafeHostIntrusionAttackService;

    /**
     * 查询主机入侵攻击列表
     */
    // @PreAuthorize("@ss.hasPermi('ffsafe:hostIntrusionAttack:list')")
    @GetMapping("/list")
    public TableDataInfo list(FfsafeHostIntrusionAttackQueryDto queryDto) {
        startPage();
        List<FfsafeHostIntrusionAttack> list = ffsafeHostIntrusionAttackService.selectFfsafeHostIntrusionAttackListByQuery(queryDto);
        return getDataTable(list);
    }

    /**
     * 导出主机入侵攻击列表
     */
    // @PreAuthorize("@ss.hasPermi('ffsafe:hostIntrusionAttack:export')")
    @Log(title = "主机入侵攻击", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, FfsafeHostIntrusionAttackQueryDto queryDto) {
        List<FfsafeHostIntrusionAttack> list = ffsafeHostIntrusionAttackService.selectFfsafeHostIntrusionAttackListByQuery(queryDto);
        ExcelUtil<FfsafeHostIntrusionAttack> util = new ExcelUtil<FfsafeHostIntrusionAttack>(FfsafeHostIntrusionAttack.class);
        util.exportExcel(response, list, "主机入侵攻击数据");
    }

    /**
     * 获取主机入侵攻击详细信息
     */
//    @PreAuthorize("@ss.hasPermi('ffsafe:hostIntrusionAttack:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(ffsafeHostIntrusionAttackService.selectFfsafeHostIntrusionAttackById(id));
    }

    /**
     * 修改主机入侵攻击
     */
    // @PreAuthorize("@ss.hasPermi('ffsafe:hostIntrusionAttack:edit')")
    @Log(title = "主机入侵攻击", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody FfsafeHostIntrusionAttack ffsafeHostIntrusionAttack) {
        ffsafeHostIntrusionAttack.setUpdateBy(getUserId().toString());
        return toAjax(ffsafeHostIntrusionAttackService.updateFfsafeHostIntrusionAttack(ffsafeHostIntrusionAttack));
    }

    /**
     * 删除单个主机入侵攻击
     */
    @PreAuthorize("@ss.hasPermi('ffsafe:hostIntrusionAttack:remove')")
    @Log(title = "主机入侵攻击", businessType = BusinessType.DELETE)
    @DeleteMapping("/{id}")
    public AjaxResult remove(@PathVariable Long id) {
        return toAjax(ffsafeHostIntrusionAttackService.deleteFfsafeHostIntrusionAttackById(id));
    }

    /**
     * 批量删除主机入侵攻击
     */
    // @PreAuthorize("@ss.hasPermi('ffsafe:hostIntrusionAttack:remove')")
    @Log(title = "主机入侵攻击", businessType = BusinessType.DELETE)
    @DeleteMapping("/batch/{ids}")
    public AjaxResult removeBatch(@PathVariable Long[] ids) {
        return toAjax(ffsafeHostIntrusionAttackService.deleteFfsafeHostIntrusionAttackByIds(ids));
    }

    /**
     * 统计满足查询条件的记录总数
     */
    @GetMapping("/statistics")
    public AjaxResult statistics(FfsafeHostIntrusionAttackQueryDto queryDto) {
        Map<String, Object> data = ffsafeHostIntrusionAttackService.selectAttackTypeStatistics(queryDto);
        return AjaxResult.success(data);
    }

    /**
     * 单个处置攻击事件
     */
    @PreAuthorize("@ss.hasPermi('ffsafe:hostIntrusionAttack:handle')")
    @Log(title = "主机入侵攻击", businessType = BusinessType.UPDATE)
    @PostMapping("/handle")
    public AjaxResult handle(@Valid @RequestBody FfsafeHostIntrusionAttackHandleDto handleDto) {
        FfsafeHostIntrusionAttack ffsafeHostIntrusionAttack = new FfsafeHostIntrusionAttack();
        ffsafeHostIntrusionAttack.setId(handleDto.getId());
        ffsafeHostIntrusionAttack.setHandleState(handleDto.getHandleState());
        ffsafeHostIntrusionAttack.setHandleDesc(handleDto.getHandleDesc());
        ffsafeHostIntrusionAttack.setDisposer(getUserId().toString());
        ffsafeHostIntrusionAttack.setUpdateBy(getUserId().toString());

        return toAjax(ffsafeHostIntrusionAttackService.updateFfsafeHostIntrusionAttack(ffsafeHostIntrusionAttack));
    }

    /**
     * 批量处置攻击事件
     */
    @PreAuthorize("@ss.hasPermi('ffsafe:hostIntrusionAttack:edit')")
    @Log(title = "主机入侵攻击", businessType = BusinessType.UPDATE)
    @PostMapping("/batchHandle")
    public AjaxResult batchHandle(@RequestBody Map<String, Object> params) {
        List<Long> eventIds = (List<Long>) params.get("eventIds");
        Integer handleState = (Integer) params.get("handleState");
        String handleDesc = (String) params.get("handleDesc");
        String disposer = getUserId().toString();

        return toAjax(ffsafeHostIntrusionAttackService.batchUpdateHandleState(eventIds, handleState, handleDesc, disposer));
    }

    /**
     * 根据attackId查询攻击详情（新增接口）
     */
    @GetMapping("/detail/{attackId}")
    public AjaxResult getAttackDetail(@PathVariable("attackId") Long attackId) {
        Map<String, Object> detail = ffsafeHostIntrusionAttackService.selectAttackDetailByAttackId(attackId);
        return AjaxResult.success(detail);
    }
}
