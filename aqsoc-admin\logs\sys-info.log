2025-08-26 15:38:55.925 [pool-6-thread-3] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=**************, operationType=UPDATE
2025-08-26 15:38:56.050 [pool-6-thread-4] INFO  c.r.s.t.FfSafeFlowAlarmTask - [syncTask,46] - 开始同步攻击者告警数据
2025-08-26 15:38:56.611 [pool-6-thread-4] INFO  c.r.f.c.FFSafeRequestComponent - [execRequest,88] - 非凡请求响应: [{"attack_ip": "************", "risk_level": 3, "ip_tags": ["内网恶意攻击"], "location": "局域网", "victim_ip_nums": 5, "attack_type_nums": 6, "attack_nums": 97, "start_time": "2025-08-25 16:17:50", "update_time": "2025-08-26 15:37:44"}, {"attack_ip": "***********", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 1, "attack_type_nums": 1, "attack_nums": 239, "start_time": "2025-08-26 09:54:16", "update_time": "2025-08-26 15:37:42"}, {"attack_ip": "***************", "risk_level": 3, "ip_tags": ["内网端口扫描", "内网恶意攻击", "暴力破解"], "location": "局域网", "victim_ip_nums": 30, "attack_type_nums": 1079, "attack_nums": 58440, "start_time": "2025-08-26 14:58:22", "update_time": "2025-08-26 15:34:10"}, {"attack_ip": "***********", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 2, "attack_type_nums": 2, "attack_nums": 155, "start_time": "2025-08-25 13:27:18", "update_time": "2025-08-26 15:33:16"}, {"attack_ip": "**************", "risk_level": 3, "ip_tags": ["内网端口扫描", "内网恶意攻击", "暴力破解"], "location": "局域网", "victim_ip_nums": 257, "attack_type_nums": 62, "attack_nums": 1176, "start_time": "2025-08-26 14:24:32", "update_time": "2025-08-26 15:32:50"}, {"attack_ip": "***********", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 1, "attack_type_nums": 3, "attack_nums": 106, "start_time": "2025-08-26 09:49:05", "update_time": "2025-08-26 15:31:50"}, {"attack_ip": "***********", "risk_level": 2, "ip_tags": ["踩中蜜罐", "内网可疑行为"], "location": "局域网", "victim_ip_nums": 2, "attack_type_nums": 6, "attack_nums": 115, "start_time": "2025-08-25 14:42:32", "update_time": "2025-08-26 15:30:35"}, {"attack_ip": "**********", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 1, "attack_type_nums": 1, "attack_nums": 403, "start_time": "2025-08-25 08:14:50", "update_time": "2025-08-26 15:29:54"}, {"attack_ip": "***********", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 1, "attack_type_nums": 1, "attack_nums": 277, "start_time": "2025-08-25 08:27:55", "update_time": "2025-08-26 15:29:41"}, {"attack_ip": "**************", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 1, "attack_type_nums": 1, "attack_nums": 13, "start_time": "2025-08-26 11:06:34", "update_time": "2025-08-26 15:28:37"}, {"attack_ip": "*************", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 2, "attack_type_nums": 1, "attack_nums": 331, "start_time": "2025-08-25 08:03:53", "update_time": "2025-08-26 15:27:58"}]

2025-08-26 15:38:56.778 [pool-6-thread-4] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:11...
2025-08-26 15:38:56.943 [pool-6-thread-4] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：0,等待任务: 0
2025-08-26 15:38:57.232 [Thread-8] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:38:57.233 [Thread-6] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:38:57.340 [Thread-8] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：11,等待任务: 0
2025-08-26 15:38:57.341 [Thread-6] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：11,等待任务: 0
2025-08-26 15:38:57.352 [async-task-pool187] INFO  c.r.f.a.e.PullHostIntrusionAttackEvent - [lambda$1,83] - 开始获取主机入侵攻击数据: 测试设备2
2025-08-26 15:38:57.361 [async-task-pool187] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,874] - 开始拉取主机入侵攻击数据，参数：HostIntrusionAttackParam(accessToken=null, sip=null, dip=null, startDate=2025-08-25 15:36:46, endDate=2025-08-26 15:36:57, page=1, pSize=20, deviceConfigId=4)
2025-08-26 15:38:57.363 [async-task-pool187] INFO  c.r.f.a.d.HostIntrusionAttackParam - [getRequestBase,80] - 非凡API主机入侵攻击分页请求参数: https://***************:23000/v2/host-edr-intrusion?access_token=lst2BAzxJTQL0eSZ&start_date=2025-08-25%2015%3A36%3A46&end_date=2025-08-26%2015%3A36%3A57&page=1&p_size=20
2025-08-26 15:38:57.352 [async-task-pool9] INFO  c.r.f.a.e.PullHostIntrusionAttackEvent - [lambda$1,83] - 开始获取主机入侵攻击数据: 测试非凡设备1
2025-08-26 15:38:57.364 [async-task-pool9] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,874] - 开始拉取主机入侵攻击数据，参数：HostIntrusionAttackParam(accessToken=null, sip=null, dip=null, startDate=2025-08-25 15:36:46, endDate=2025-08-26 15:36:57, page=1, pSize=20, deviceConfigId=1)
2025-08-26 15:38:57.367 [async-task-pool9] INFO  c.r.f.a.d.HostIntrusionAttackParam - [getRequestBase,80] - 非凡API主机入侵攻击分页请求参数: https://***************:23000/v2/host-edr-intrusion?access_token=lst2BAzxJTQL0eSZ&start_date=2025-08-25%2015%3A36%3A46&end_date=2025-08-26%2015%3A36%3A57&page=1&p_size=20
2025-08-26 15:38:57.352 [async-task-pool179] INFO  c.r.f.a.e.PullFilterLogEvent - [lambda$1,90] - 开始获取IP阻断日志: 测试非凡设备1
2025-08-26 15:38:57.352 [async-task-pool165] INFO  c.r.f.a.e.PullFilterLogEvent - [lambda$1,90] - 开始获取IP阻断日志: 测试设备2
2025-08-26 15:38:58.101 [taskScheduler-31] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:38:58.104 [taskScheduler-31] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：12,等待任务: 0
2025-08-26 15:38:58.221 [async-task-pool155] INFO  c.r.f.c.FFSafeRequestComponent - [execRequest,88] - 非凡请求响应: [{"id": 1058, "block_ip": "*************", "location": ["美国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-22 00:58:57", "release_time": "2025-08-29 01:00:59", "remarks": ""}, {"id": 1057, "block_ip": "************", "location": ["法国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-21 13:55:52", "release_time": "2025-08-28 13:56:53", "remarks": ""}, {"id": 1056, "block_ip": "***********", "location": ["印度"], "action": "阻断", "conditions": ["外网恶意攻击"], "type": "流量检测", "create_time": "2025-08-20 12:44:55", "release_time": "2025-08-27 12:46:57", "remarks": ""}]

2025-08-26 15:38:58.237 [Thread-7] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:38:58.242 [Thread-7] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：8,等待任务: 0
2025-08-26 15:38:58.243 [async-task-pool15] INFO  c.r.f.a.e.PullFlowRiskAssetsEvent - [lambda$1,100] - 开始获取流量风险资产数据: 测试设备2
2025-08-26 15:38:58.243 [async-task-pool18] INFO  c.r.f.a.e.PullFlowRiskAssetsEvent - [lambda$1,100] - 开始获取流量风险资产数据: 测试非凡设备1
2025-08-26 15:38:58.243 [async-task-pool15] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,352] - 开始拉取流量风险资产数据，参数：FlowRiskAssetsParam(accessToken=null, riskType=all, startTime=2025-08-25 15:36:57, endTime=2025-08-26 15:36:58, page=1, pageSize=30, deviceConfigId=4)
2025-08-26 15:38:58.245 [async-task-pool18] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,352] - 开始拉取流量风险资产数据，参数：FlowRiskAssetsParam(accessToken=null, riskType=all, startTime=2025-08-25 15:36:57, endTime=2025-08-26 15:36:58, page=1, pageSize=30, deviceConfigId=1)
2025-08-26 15:38:58.247 [async-task-pool15] INFO  c.r.f.a.d.FlowRiskAssetsParam - [getRequestBase,49] - 非凡API流量风险资产请求参数: https://***************:23000/v2/flow-risk-assets?access_token=lst2BAzxJTQL0eSZ&risk_type=all&start_date=2025-08-25%2015%3A36%3A57&end_date=2025-08-26%2015%3A36%3A58&page=1&page_size=30
2025-08-26 15:38:58.248 [async-task-pool18] INFO  c.r.f.a.d.FlowRiskAssetsParam - [getRequestBase,49] - 非凡API流量风险资产请求参数: https://***************:23000/v2/flow-risk-assets?access_token=lst2BAzxJTQL0eSZ&risk_type=all&start_date=2025-08-25%2015%3A36%3A57&end_date=2025-08-26%2015%3A36%3A58&page=1&page_size=30
2025-08-26 15:38:58.277 [async-task-pool9] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,987] - 主机入侵攻击总数: 0
2025-08-26 15:38:58.278 [async-task-pool9] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1010] - 第1页获取0条数据
2025-08-26 15:38:58.283 [async-task-pool187] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,987] - 主机入侵攻击总数: 0
2025-08-26 15:38:58.286 [async-task-pool9] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1014] - 当前页无数据，停止获取
2025-08-26 15:38:58.286 [async-task-pool187] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1010] - 第1页获取0条数据
2025-08-26 15:38:58.286 [async-task-pool9] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1083] - 拉取主机入侵攻击数据成功，但没有有效数据
2025-08-26 15:38:58.295 [async-task-pool187] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1014] - 当前页无数据，停止获取
2025-08-26 15:38:58.297 [async-task-pool187] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1083] - 拉取主机入侵攻击数据成功，但没有有效数据
2025-08-26 15:38:58.309 [async-task-pool173] INFO  c.r.f.c.FFSafeRequestComponent - [execRequest,88] - 非凡请求响应: [{"id": 1058, "block_ip": "*************", "location": ["美国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-22 00:58:57", "release_time": "2025-08-29 01:00:59", "remarks": ""}, {"id": 1057, "block_ip": "************", "location": ["法国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-21 13:55:52", "release_time": "2025-08-28 13:56:53", "remarks": ""}, {"id": 1056, "block_ip": "***********", "location": ["印度"], "action": "阻断", "conditions": ["外网恶意攻击"], "type": "流量检测", "create_time": "2025-08-20 12:44:55", "release_time": "2025-08-27 12:46:57", "remarks": ""}]

2025-08-26 15:38:58.332 [async-task-pool15] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,458] - 流量风险资产总数: 6
2025-08-26 15:38:58.338 [async-task-pool15] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,481] - 第1页获取6条数据
2025-08-26 15:38:58.340 [async-task-pool15] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,488] - 当前页数据量(6)少于页大小(30)，已获取完所有数据
2025-08-26 15:38:58.371 [async-task-pool18] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,458] - 流量风险资产总数: 6
2025-08-26 15:38:58.379 [Thread-8] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 1秒147毫秒
2025-08-26 15:38:58.379 [async-task-pool18] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,481] - 第1页获取6条数据
2025-08-26 15:38:58.380 [async-task-pool18] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,488] - 当前页数据量(6)少于页大小(30)，已获取完所有数据
2025-08-26 15:38:58.393 [Thread-6] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 1秒160毫秒
2025-08-26 15:38:58.409 [taskScheduler-31] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 308毫秒
2025-08-26 15:38:58.412 [async-task-pool15] INFO  c.r.m.c.c.FfsafeClientService - [processBatch,662] - 批量更新6条流量风险资产数据
2025-08-26 15:38:58.427 [async-task-pool15] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,551] - 拉取流量风险资产数据完成，耗时184ms，共1页，共6条数据，处理结果: true
2025-08-26 15:38:58.468 [async-task-pool18] INFO  c.r.m.c.c.FfsafeClientService - [processBatch,662] - 批量更新6条流量风险资产数据
2025-08-26 15:38:58.478 [async-task-pool18] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,551] - 拉取流量风险资产数据完成，耗时233ms，共1页，共6条数据，处理结果: true
2025-08-26 15:38:58.521 [Thread-7] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 284毫秒
2025-08-26 15:38:59.947 [pool-4-thread-1] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [lambda$0,81] - 最大连接：200 当前连接: 2
2025-08-26 15:39:02.839 [Thread-5] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:39:02.840 [Thread-5] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：1,等待任务: 0
2025-08-26 15:39:02.840 [async-task-pool17] INFO  c.r.f.a.e.PullDataEvent - [lambda$1,102] - 开始流量告警日志同步: 测试设备2
2025-08-26 15:39:02.840 [async-task-pool20] INFO  c.r.f.a.e.PullDataEvent - [lambda$1,102] - 开始流量告警日志同步: 测试非凡设备1
2025-08-26 15:39:03.031 [Thread-5] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 192毫秒
2025-08-26 15:39:08.636 [Thread-6] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:39:08.637 [Thread-8] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:39:08.638 [Thread-6] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：1,等待任务: 0
2025-08-26 15:39:08.642 [Thread-8] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：1,等待任务: 0
2025-08-26 15:39:08.644 [async-task-pool23] INFO  c.r.f.a.e.PullFilterLogEvent - [lambda$1,90] - 开始获取IP阻断日志: 测试设备2
2025-08-26 15:39:08.644 [async-task-pool13] INFO  c.r.f.a.e.PullFilterLogEvent - [lambda$1,90] - 开始获取IP阻断日志: 测试非凡设备1
2025-08-26 15:39:08.675 [taskScheduler-31] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:39:08.644 [async-task-pool40] INFO  c.r.f.a.e.PullHostIntrusionAttackEvent - [lambda$1,83] - 开始获取主机入侵攻击数据: 测试设备2
2025-08-26 15:39:08.644 [async-task-pool5] INFO  c.r.f.a.e.PullHostIntrusionAttackEvent - [lambda$1,83] - 开始获取主机入侵攻击数据: 测试非凡设备1
2025-08-26 15:39:08.676 [taskScheduler-31] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：5,等待任务: 0
2025-08-26 15:39:08.677 [async-task-pool40] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,874] - 开始拉取主机入侵攻击数据，参数：HostIntrusionAttackParam(accessToken=null, sip=null, dip=null, startDate=2025-08-25 15:36:57, endDate=2025-08-26 15:37:08, page=1, pSize=20, deviceConfigId=4)
2025-08-26 15:39:08.678 [async-task-pool5] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,874] - 开始拉取主机入侵攻击数据，参数：HostIntrusionAttackParam(accessToken=null, sip=null, dip=null, startDate=2025-08-25 15:36:57, endDate=2025-08-26 15:37:08, page=1, pSize=20, deviceConfigId=1)
2025-08-26 15:39:08.689 [async-task-pool40] INFO  c.r.f.a.d.HostIntrusionAttackParam - [getRequestBase,80] - 非凡API主机入侵攻击分页请求参数: https://***************:23000/v2/host-edr-intrusion?access_token=lst2BAzxJTQL0eSZ&start_date=2025-08-25%2015%3A36%3A57&end_date=2025-08-26%2015%3A37%3A08&page=1&p_size=20
2025-08-26 15:39:08.689 [Thread-7] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:39:08.689 [async-task-pool5] INFO  c.r.f.a.d.HostIntrusionAttackParam - [getRequestBase,80] - 非凡API主机入侵攻击分页请求参数: https://***************:23000/v2/host-edr-intrusion?access_token=lst2BAzxJTQL0eSZ&start_date=2025-08-25%2015%3A36%3A57&end_date=2025-08-26%2015%3A37%3A08&page=1&p_size=20
2025-08-26 15:39:08.690 [Thread-7] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：7,等待任务: 0
2025-08-26 15:39:08.705 [async-task-pool147] INFO  c.r.f.a.e.PullFlowRiskAssetsEvent - [lambda$1,100] - 开始获取流量风险资产数据: 测试非凡设备1
2025-08-26 15:39:08.706 [async-task-pool169] INFO  c.r.f.a.e.PullFlowRiskAssetsEvent - [lambda$1,100] - 开始获取流量风险资产数据: 测试设备2
2025-08-26 15:39:08.706 [async-task-pool147] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,352] - 开始拉取流量风险资产数据，参数：FlowRiskAssetsParam(accessToken=null, riskType=all, startTime=2025-08-25 15:37:07, endTime=2025-08-26 15:37:08, page=1, pageSize=30, deviceConfigId=1)
2025-08-26 15:39:08.706 [async-task-pool169] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,352] - 开始拉取流量风险资产数据，参数：FlowRiskAssetsParam(accessToken=null, riskType=all, startTime=2025-08-25 15:37:07, endTime=2025-08-26 15:37:08, page=1, pageSize=30, deviceConfigId=4)
2025-08-26 15:39:08.708 [async-task-pool147] INFO  c.r.f.a.d.FlowRiskAssetsParam - [getRequestBase,49] - 非凡API流量风险资产请求参数: https://***************:23000/v2/flow-risk-assets?access_token=lst2BAzxJTQL0eSZ&risk_type=all&start_date=2025-08-25%2015%3A37%3A07&end_date=2025-08-26%2015%3A37%3A08&page=1&page_size=30
2025-08-26 15:39:08.709 [async-task-pool169] INFO  c.r.f.a.d.FlowRiskAssetsParam - [getRequestBase,49] - 非凡API流量风险资产请求参数: https://***************:23000/v2/flow-risk-assets?access_token=lst2BAzxJTQL0eSZ&risk_type=all&start_date=2025-08-25%2015%3A37%3A07&end_date=2025-08-26%2015%3A37%3A08&page=1&page_size=30
2025-08-26 15:39:08.942 [async-task-pool186] INFO  c.r.f.c.FFSafeRequestComponent - [execRequest,88] - 非凡请求响应: [{"id": 1058, "block_ip": "*************", "location": ["美国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-22 00:58:57", "release_time": "2025-08-29 01:00:59", "remarks": ""}, {"id": 1057, "block_ip": "************", "location": ["法国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-21 13:55:52", "release_time": "2025-08-28 13:56:53", "remarks": ""}, {"id": 1056, "block_ip": "***********", "location": ["印度"], "action": "阻断", "conditions": ["外网恶意攻击"], "type": "流量检测", "create_time": "2025-08-20 12:44:55", "release_time": "2025-08-27 12:46:57", "remarks": ""}]

2025-08-26 15:39:08.946 [async-task-pool40] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,987] - 主机入侵攻击总数: 0
2025-08-26 15:39:08.953 [async-task-pool40] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1010] - 第1页获取0条数据
2025-08-26 15:39:08.981 [async-task-pool169] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,458] - 流量风险资产总数: 6
2025-08-26 15:39:09.003 [async-task-pool42] INFO  c.r.f.c.FFSafeRequestComponent - [execRequest,88] - 非凡请求响应: [{"id": 1058, "block_ip": "*************", "location": ["美国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-22 00:58:57", "release_time": "2025-08-29 01:00:59", "remarks": ""}, {"id": 1057, "block_ip": "************", "location": ["法国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-21 13:55:52", "release_time": "2025-08-28 13:56:53", "remarks": ""}, {"id": 1056, "block_ip": "***********", "location": ["印度"], "action": "阻断", "conditions": ["外网恶意攻击"], "type": "流量检测", "create_time": "2025-08-20 12:44:55", "release_time": "2025-08-27 12:46:57", "remarks": ""}]

2025-08-26 15:39:09.004 [async-task-pool147] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,458] - 流量风险资产总数: 6
2025-08-26 15:39:09.011 [async-task-pool40] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1014] - 当前页无数据，停止获取
2025-08-26 15:39:09.070 [async-task-pool40] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1083] - 拉取主机入侵攻击数据成功，但没有有效数据
2025-08-26 15:39:09.047 [async-task-pool5] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,987] - 主机入侵攻击总数: 0
2025-08-26 15:39:09.052 [async-task-pool169] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,481] - 第1页获取6条数据
2025-08-26 15:39:09.075 [async-task-pool169] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,488] - 当前页数据量(6)少于页大小(30)，已获取完所有数据
2025-08-26 15:39:09.053 [async-task-pool147] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,481] - 第1页获取6条数据
2025-08-26 15:39:09.071 [async-task-pool5] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1010] - 第1页获取0条数据
2025-08-26 15:39:09.078 [async-task-pool147] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,488] - 当前页数据量(6)少于页大小(30)，已获取完所有数据
2025-08-26 15:39:09.085 [async-task-pool5] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1014] - 当前页无数据，停止获取
2025-08-26 15:39:09.091 [async-task-pool5] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1083] - 拉取主机入侵攻击数据成功，但没有有效数据
2025-08-26 15:39:09.113 [taskScheduler-31] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 438毫秒
2025-08-26 15:39:09.122 [Thread-8] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 485毫秒
2025-08-26 15:39:09.123 [Thread-6] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 487毫秒
2025-08-26 15:39:09.144 [async-task-pool147] INFO  c.r.m.c.c.FfsafeClientService - [processBatch,662] - 批量更新6条流量风险资产数据
2025-08-26 15:39:09.144 [async-task-pool147] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,551] - 拉取流量风险资产数据完成，耗时438ms，共1页，共6条数据，处理结果: true
2025-08-26 15:39:09.163 [async-task-pool169] INFO  c.r.m.c.c.FfsafeClientService - [processBatch,662] - 批量更新6条流量风险资产数据
2025-08-26 15:39:09.168 [async-task-pool169] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,551] - 拉取流量风险资产数据完成，耗时462ms，共1页，共6条数据，处理结果: true
2025-08-26 15:39:09.175 [Thread-7] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 486毫秒
2025-08-26 15:39:09.948 [pool-4-thread-1] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [lambda$0,81] - 最大连接：200 当前连接: 2
2025-08-26 15:39:10.893 [pool-6-thread-4] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 14秒115毫秒
2025-08-26 15:39:11.300 [pool-6-thread-4] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sync,255] - 批量更新攻击者视角告警列开始: 11
2025-08-26 15:39:11.336 [pool-6-thread-4] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=************, operationType=UPDATE
2025-08-26 15:39:11.339 [pool-6-thread-4] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=***********, operationType=UPDATE
2025-08-26 15:39:11.340 [pool-6-thread-4] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=***************, operationType=UPDATE
2025-08-26 15:39:11.344 [pool-6-thread-4] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=***********, operationType=UPDATE
2025-08-26 15:39:11.346 [pool-6-thread-4] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=**************, operationType=UPDATE
2025-08-26 15:39:11.347 [pool-6-thread-4] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=***********, operationType=UPDATE
2025-08-26 15:39:11.350 [pool-6-thread-4] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=***********, operationType=UPDATE
2025-08-26 15:39:11.357 [pool-6-thread-4] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=**********, operationType=UPDATE
2025-08-26 15:39:11.365 [pool-6-thread-4] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=***********, operationType=UPDATE
2025-08-26 15:39:11.369 [pool-6-thread-4] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=**************, operationType=UPDATE
2025-08-26 15:39:11.370 [pool-6-thread-4] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=*************, operationType=UPDATE
2025-08-26 15:39:11.373 [pool-6-thread-1] INFO  c.r.s.t.FfSafeFlowAlarmTask - [syncTask,46] - 开始同步攻击者告警数据
2025-08-26 15:39:11.419 [pool-6-thread-1] INFO  c.r.f.c.FFSafeRequestComponent - [execRequest,88] - 非凡请求响应: [{"attack_ip": "***************", "risk_level": 3, "ip_tags": ["内网端口扫描", "内网恶意攻击", "暴力破解"], "location": "局域网", "victim_ip_nums": 30, "attack_type_nums": 1079, "attack_nums": 58440, "start_time": "2025-08-26 14:58:22", "update_time": "2025-08-26 15:34:10"}, {"attack_ip": "***********", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 2, "attack_type_nums": 2, "attack_nums": 155, "start_time": "2025-08-25 13:27:18", "update_time": "2025-08-26 15:33:16"}, {"attack_ip": "**************", "risk_level": 3, "ip_tags": ["内网端口扫描", "内网恶意攻击", "暴力破解"], "location": "局域网", "victim_ip_nums": 257, "attack_type_nums": 62, "attack_nums": 1176, "start_time": "2025-08-26 14:24:32", "update_time": "2025-08-26 15:32:50"}, {"attack_ip": "***********", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 1, "attack_type_nums": 3, "attack_nums": 106, "start_time": "2025-08-26 09:49:05", "update_time": "2025-08-26 15:31:50"}, {"attack_ip": "***********", "risk_level": 2, "ip_tags": ["踩中蜜罐", "内网可疑行为"], "location": "局域网", "victim_ip_nums": 2, "attack_type_nums": 6, "attack_nums": 115, "start_time": "2025-08-25 14:42:32", "update_time": "2025-08-26 15:30:35"}, {"attack_ip": "**********", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 1, "attack_type_nums": 1, "attack_nums": 403, "start_time": "2025-08-25 08:14:50", "update_time": "2025-08-26 15:29:54"}, {"attack_ip": "***********", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 1, "attack_type_nums": 1, "attack_nums": 277, "start_time": "2025-08-25 08:27:55", "update_time": "2025-08-26 15:29:41"}, {"attack_ip": "**************", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 1, "attack_type_nums": 1, "attack_nums": 13, "start_time": "2025-08-26 11:06:34", "update_time": "2025-08-26 15:28:37"}, {"attack_ip": "*************", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 2, "attack_type_nums": 1, "attack_nums": 331, "start_time": "2025-08-25 08:03:53", "update_time": "2025-08-26 15:27:58"}]

2025-08-26 15:39:11.433 [pool-6-thread-1] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:9...
2025-08-26 15:39:11.437 [pool-6-thread-1] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：0,等待任务: 0
2025-08-26 15:39:13.037 [Thread-5] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:39:13.038 [Thread-5] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：1,等待任务: 0
2025-08-26 15:39:13.038 [async-task-pool43] INFO  c.r.f.a.e.PullDataEvent - [lambda$1,102] - 开始流量告警日志同步: 测试设备2
2025-08-26 15:39:13.038 [async-task-pool174] INFO  c.r.f.a.e.PullDataEvent - [lambda$1,102] - 开始流量告警日志同步: 测试非凡设备1
2025-08-26 15:39:14.158 [Thread-5] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 1秒121毫秒
2025-08-26 15:39:14.171 [pool-5-thread-1] INFO  c.r.s.t.TblDeductionDetailTask - [syncThreaten,202] - 处理威胁告警扣分结束
2025-08-26 15:39:19.129 [taskScheduler-31] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:39:19.129 [taskScheduler-31] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：1,等待任务: 0
2025-08-26 15:39:19.144 [Thread-8] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:39:19.144 [Thread-8] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：3,等待任务: 0
2025-08-26 15:39:19.145 [async-task-pool24] INFO  c.r.f.a.e.PullHostIntrusionAttackEvent - [lambda$1,83] - 开始获取主机入侵攻击数据: 测试设备2
2025-08-26 15:39:19.145 [async-task-pool59] INFO  c.r.f.a.e.PullHostIntrusionAttackEvent - [lambda$1,83] - 开始获取主机入侵攻击数据: 测试非凡设备1
2025-08-26 15:39:19.146 [async-task-pool24] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,874] - 开始拉取主机入侵攻击数据，参数：HostIntrusionAttackParam(accessToken=null, sip=null, dip=null, startDate=2025-08-25 15:37:08, endDate=2025-08-26 15:37:19, page=1, pSize=20, deviceConfigId=4)
2025-08-26 15:39:19.146 [async-task-pool59] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,874] - 开始拉取主机入侵攻击数据，参数：HostIntrusionAttackParam(accessToken=null, sip=null, dip=null, startDate=2025-08-25 15:37:08, endDate=2025-08-26 15:37:19, page=1, pSize=20, deviceConfigId=1)
2025-08-26 15:39:19.146 [Thread-6] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:39:19.148 [async-task-pool24] INFO  c.r.f.a.d.HostIntrusionAttackParam - [getRequestBase,80] - 非凡API主机入侵攻击分页请求参数: https://***************:23000/v2/host-edr-intrusion?access_token=lst2BAzxJTQL0eSZ&start_date=2025-08-25%2015%3A37%3A08&end_date=2025-08-26%2015%3A37%3A19&page=1&p_size=20
2025-08-26 15:39:19.148 [Thread-6] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：5,等待任务: 0
2025-08-26 15:39:19.148 [async-task-pool59] INFO  c.r.f.a.d.HostIntrusionAttackParam - [getRequestBase,80] - 非凡API主机入侵攻击分页请求参数: https://***************:23000/v2/host-edr-intrusion?access_token=lst2BAzxJTQL0eSZ&start_date=2025-08-25%2015%3A37%3A08&end_date=2025-08-26%2015%3A37%3A19&page=1&p_size=20
2025-08-26 15:39:19.149 [async-task-pool63] INFO  c.r.f.a.e.PullFilterLogEvent - [lambda$1,90] - 开始获取IP阻断日志: 测试设备2
2025-08-26 15:39:19.149 [async-task-pool35] INFO  c.r.f.a.e.PullFilterLogEvent - [lambda$1,90] - 开始获取IP阻断日志: 测试非凡设备1
2025-08-26 15:39:19.191 [Thread-7] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:39:19.195 [Thread-7] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：7,等待任务: 0
2025-08-26 15:39:19.198 [async-task-pool56] INFO  c.r.f.a.e.PullFlowRiskAssetsEvent - [lambda$1,100] - 开始获取流量风险资产数据: 测试设备2
2025-08-26 15:39:19.198 [async-task-pool48] INFO  c.r.f.a.e.PullFlowRiskAssetsEvent - [lambda$1,100] - 开始获取流量风险资产数据: 测试非凡设备1
2025-08-26 15:39:19.199 [async-task-pool56] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,352] - 开始拉取流量风险资产数据，参数：FlowRiskAssetsParam(accessToken=null, riskType=all, startTime=2025-08-25 15:37:17, endTime=2025-08-26 15:37:19, page=1, pageSize=30, deviceConfigId=4)
2025-08-26 15:39:19.199 [async-task-pool48] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,352] - 开始拉取流量风险资产数据，参数：FlowRiskAssetsParam(accessToken=null, riskType=all, startTime=2025-08-25 15:37:17, endTime=2025-08-26 15:37:19, page=1, pageSize=30, deviceConfigId=1)
2025-08-26 15:39:19.202 [async-task-pool56] INFO  c.r.f.a.d.FlowRiskAssetsParam - [getRequestBase,49] - 非凡API流量风险资产请求参数: https://***************:23000/v2/flow-risk-assets?access_token=lst2BAzxJTQL0eSZ&risk_type=all&start_date=2025-08-25%2015%3A37%3A17&end_date=2025-08-26%2015%3A37%3A19&page=1&page_size=30
2025-08-26 15:39:19.203 [async-task-pool48] INFO  c.r.f.a.d.FlowRiskAssetsParam - [getRequestBase,49] - 非凡API流量风险资产请求参数: https://***************:23000/v2/flow-risk-assets?access_token=lst2BAzxJTQL0eSZ&risk_type=all&start_date=2025-08-25%2015%3A37%3A17&end_date=2025-08-26%2015%3A37%3A19&page=1&page_size=30
2025-08-26 15:39:19.239 [async-task-pool27] INFO  c.r.f.c.FFSafeRequestComponent - [execRequest,88] - 非凡请求响应: [{"id": 1058, "block_ip": "*************", "location": ["美国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-22 00:58:57", "release_time": "2025-08-29 01:00:59", "remarks": ""}, {"id": 1057, "block_ip": "************", "location": ["法国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-21 13:55:52", "release_time": "2025-08-28 13:56:53", "remarks": ""}, {"id": 1056, "block_ip": "***********", "location": ["印度"], "action": "阻断", "conditions": ["外网恶意攻击"], "type": "流量检测", "create_time": "2025-08-20 12:44:55", "release_time": "2025-08-27 12:46:57", "remarks": ""}]

2025-08-26 15:39:19.249 [async-task-pool24] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,987] - 主机入侵攻击总数: 0
2025-08-26 15:39:19.250 [async-task-pool21] INFO  c.r.f.c.FFSafeRequestComponent - [execRequest,88] - 非凡请求响应: [{"id": 1058, "block_ip": "*************", "location": ["美国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-22 00:58:57", "release_time": "2025-08-29 01:00:59", "remarks": ""}, {"id": 1057, "block_ip": "************", "location": ["法国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-21 13:55:52", "release_time": "2025-08-28 13:56:53", "remarks": ""}, {"id": 1056, "block_ip": "***********", "location": ["印度"], "action": "阻断", "conditions": ["外网恶意攻击"], "type": "流量检测", "create_time": "2025-08-20 12:44:55", "release_time": "2025-08-27 12:46:57", "remarks": ""}]

2025-08-26 15:39:19.251 [async-task-pool24] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1010] - 第1页获取0条数据
2025-08-26 15:39:19.254 [async-task-pool24] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1014] - 当前页无数据，停止获取
2025-08-26 15:39:19.255 [async-task-pool59] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,987] - 主机入侵攻击总数: 0
2025-08-26 15:39:19.261 [async-task-pool24] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1083] - 拉取主机入侵攻击数据成功，但没有有效数据
2025-08-26 15:39:19.261 [async-task-pool59] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1010] - 第1页获取0条数据
2025-08-26 15:39:19.262 [async-task-pool59] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1014] - 当前页无数据，停止获取
2025-08-26 15:39:19.264 [async-task-pool59] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1083] - 拉取主机入侵攻击数据成功，但没有有效数据
2025-08-26 15:39:19.270 [Thread-6] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 124毫秒
2025-08-26 15:39:19.276 [taskScheduler-31] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 147毫秒
2025-08-26 15:39:19.284 [Thread-8] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 140毫秒
2025-08-26 15:39:19.287 [async-task-pool48] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,458] - 流量风险资产总数: 6
2025-08-26 15:39:19.291 [async-task-pool48] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,481] - 第1页获取6条数据
2025-08-26 15:39:19.294 [async-task-pool48] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,488] - 当前页数据量(6)少于页大小(30)，已获取完所有数据
2025-08-26 15:39:19.295 [async-task-pool56] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,458] - 流量风险资产总数: 6
2025-08-26 15:39:19.298 [async-task-pool56] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,481] - 第1页获取6条数据
2025-08-26 15:39:19.299 [async-task-pool56] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,488] - 当前页数据量(6)少于页大小(30)，已获取完所有数据
2025-08-26 15:39:19.326 [async-task-pool48] INFO  c.r.m.c.c.FfsafeClientService - [processBatch,662] - 批量更新6条流量风险资产数据
2025-08-26 15:39:19.331 [async-task-pool48] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,551] - 拉取流量风险资产数据完成，耗时132ms，共1页，共6条数据，处理结果: true
2025-08-26 15:39:19.335 [async-task-pool56] INFO  c.r.m.c.c.FfsafeClientService - [processBatch,662] - 批量更新6条流量风险资产数据
2025-08-26 15:39:19.335 [async-task-pool56] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,551] - 拉取流量风险资产数据完成，耗时136ms，共1页，共6条数据，处理结果: true
2025-08-26 15:39:19.345 [Thread-7] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 154毫秒
2025-08-26 15:39:19.950 [pool-4-thread-1] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [lambda$0,81] - 最大连接：200 当前连接: 2
2025-08-26 15:39:24.183 [Thread-5] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:39:24.185 [Thread-5] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：1,等待任务: 0
2025-08-26 15:39:24.189 [async-task-pool32] INFO  c.r.f.a.e.PullDataEvent - [lambda$1,102] - 开始流量告警日志同步: 测试非凡设备1
2025-08-26 15:39:24.189 [async-task-pool30] INFO  c.r.f.a.e.PullDataEvent - [lambda$1,102] - 开始流量告警日志同步: 测试设备2
2025-08-26 15:39:24.392 [Thread-5] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 209毫秒
2025-08-26 15:39:27.136 [pool-6-thread-1] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 15秒703毫秒
2025-08-26 15:39:27.799 [pool-6-thread-1] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sync,255] - 批量更新攻击者视角告警列开始: 9
2025-08-26 15:39:27.838 [pool-6-thread-1] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=***************, operationType=UPDATE
2025-08-26 15:39:27.843 [pool-6-thread-1] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=***********, operationType=UPDATE
2025-08-26 15:39:27.845 [pool-6-thread-1] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=**************, operationType=UPDATE
2025-08-26 15:39:27.851 [pool-6-thread-1] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=***********, operationType=UPDATE
2025-08-26 15:39:27.855 [pool-6-thread-1] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=***********, operationType=UPDATE
2025-08-26 15:39:27.869 [pool-6-thread-1] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=**********, operationType=UPDATE
2025-08-26 15:39:27.871 [pool-6-thread-1] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=***********, operationType=UPDATE
2025-08-26 15:39:27.876 [pool-6-thread-1] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=**************, operationType=UPDATE
2025-08-26 15:39:27.893 [pool-6-thread-1] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=*************, operationType=UPDATE
2025-08-26 15:39:27.907 [pool-6-thread-5] INFO  c.r.s.t.FfSafeFlowAlarmTask - [syncTask,46] - 开始同步攻击者告警数据
2025-08-26 15:39:27.994 [pool-6-thread-5] INFO  c.r.f.c.FFSafeRequestComponent - [execRequest,88] - 非凡请求响应: [{"attack_ip": "***************", "risk_level": 3, "ip_tags": ["内网端口扫描", "内网恶意攻击", "暴力破解"], "location": "局域网", "victim_ip_nums": 30, "attack_type_nums": 1079, "attack_nums": 58440, "start_time": "2025-08-26 14:58:22", "update_time": "2025-08-26 15:34:10"}, {"attack_ip": "***********", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 2, "attack_type_nums": 2, "attack_nums": 155, "start_time": "2025-08-25 13:27:18", "update_time": "2025-08-26 15:33:16"}, {"attack_ip": "**************", "risk_level": 3, "ip_tags": ["内网端口扫描", "内网恶意攻击", "暴力破解"], "location": "局域网", "victim_ip_nums": 257, "attack_type_nums": 62, "attack_nums": 1176, "start_time": "2025-08-26 14:24:32", "update_time": "2025-08-26 15:32:50"}, {"attack_ip": "***********", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 1, "attack_type_nums": 3, "attack_nums": 106, "start_time": "2025-08-26 09:49:05", "update_time": "2025-08-26 15:31:50"}, {"attack_ip": "***********", "risk_level": 2, "ip_tags": ["踩中蜜罐", "内网可疑行为"], "location": "局域网", "victim_ip_nums": 2, "attack_type_nums": 6, "attack_nums": 115, "start_time": "2025-08-25 14:42:32", "update_time": "2025-08-26 15:30:35"}, {"attack_ip": "**********", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 1, "attack_type_nums": 1, "attack_nums": 403, "start_time": "2025-08-25 08:14:50", "update_time": "2025-08-26 15:29:54"}, {"attack_ip": "***********", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 1, "attack_type_nums": 1, "attack_nums": 277, "start_time": "2025-08-25 08:27:55", "update_time": "2025-08-26 15:29:41"}, {"attack_ip": "**************", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 1, "attack_type_nums": 1, "attack_nums": 13, "start_time": "2025-08-26 11:06:34", "update_time": "2025-08-26 15:28:37"}, {"attack_ip": "*************", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 2, "attack_type_nums": 1, "attack_nums": 331, "start_time": "2025-08-25 08:03:53", "update_time": "2025-08-26 15:27:58"}]

2025-08-26 15:39:28.004 [pool-6-thread-5] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:9...
2025-08-26 15:39:28.005 [pool-6-thread-5] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：0,等待任务: 0
2025-08-26 15:39:29.274 [Thread-6] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:39:29.274 [Thread-6] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：1,等待任务: 0
2025-08-26 15:39:29.274 [async-task-pool62] INFO  c.r.f.a.e.PullFilterLogEvent - [lambda$1,90] - 开始获取IP阻断日志: 测试设备2
2025-08-26 15:39:29.274 [async-task-pool67] INFO  c.r.f.a.e.PullFilterLogEvent - [lambda$1,90] - 开始获取IP阻断日志: 测试非凡设备1
2025-08-26 15:39:29.286 [taskScheduler-31] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:39:29.287 [taskScheduler-31] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：3,等待任务: 0
2025-08-26 15:39:29.293 [Thread-8] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:39:29.298 [Thread-8] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：5,等待任务: 0
2025-08-26 15:39:29.299 [async-task-pool61] INFO  c.r.f.a.e.PullHostIntrusionAttackEvent - [lambda$1,83] - 开始获取主机入侵攻击数据: 测试非凡设备1
2025-08-26 15:39:29.299 [async-task-pool181] INFO  c.r.f.a.e.PullHostIntrusionAttackEvent - [lambda$1,83] - 开始获取主机入侵攻击数据: 测试设备2
2025-08-26 15:39:29.299 [async-task-pool61] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,874] - 开始拉取主机入侵攻击数据，参数：HostIntrusionAttackParam(accessToken=null, sip=null, dip=null, startDate=2025-08-25 15:37:19, endDate=2025-08-26 15:37:29, page=1, pSize=20, deviceConfigId=1)
2025-08-26 15:39:29.300 [async-task-pool181] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,874] - 开始拉取主机入侵攻击数据，参数：HostIntrusionAttackParam(accessToken=null, sip=null, dip=null, startDate=2025-08-25 15:37:19, endDate=2025-08-26 15:37:29, page=1, pSize=20, deviceConfigId=4)
2025-08-26 15:39:29.301 [async-task-pool61] INFO  c.r.f.a.d.HostIntrusionAttackParam - [getRequestBase,80] - 非凡API主机入侵攻击分页请求参数: https://***************:23000/v2/host-edr-intrusion?access_token=lst2BAzxJTQL0eSZ&start_date=2025-08-25%2015%3A37%3A19&end_date=2025-08-26%2015%3A37%3A29&page=1&p_size=20
2025-08-26 15:39:29.302 [async-task-pool181] INFO  c.r.f.a.d.HostIntrusionAttackParam - [getRequestBase,80] - 非凡API主机入侵攻击分页请求参数: https://***************:23000/v2/host-edr-intrusion?access_token=lst2BAzxJTQL0eSZ&start_date=2025-08-25%2015%3A37%3A19&end_date=2025-08-26%2015%3A37%3A29&page=1&p_size=20
2025-08-26 15:39:29.351 [async-task-pool69] INFO  c.r.f.c.FFSafeRequestComponent - [execRequest,88] - 非凡请求响应: [{"id": 1058, "block_ip": "*************", "location": ["美国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-22 00:58:57", "release_time": "2025-08-29 01:00:59", "remarks": ""}, {"id": 1057, "block_ip": "************", "location": ["法国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-21 13:55:52", "release_time": "2025-08-28 13:56:53", "remarks": ""}, {"id": 1056, "block_ip": "***********", "location": ["印度"], "action": "阻断", "conditions": ["外网恶意攻击"], "type": "流量检测", "create_time": "2025-08-20 12:44:55", "release_time": "2025-08-27 12:46:57", "remarks": ""}]

2025-08-26 15:39:29.352 [async-task-pool65] INFO  c.r.f.c.FFSafeRequestComponent - [execRequest,88] - 非凡请求响应: [{"id": 1058, "block_ip": "*************", "location": ["美国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-22 00:58:57", "release_time": "2025-08-29 01:00:59", "remarks": ""}, {"id": 1057, "block_ip": "************", "location": ["法国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-21 13:55:52", "release_time": "2025-08-28 13:56:53", "remarks": ""}, {"id": 1056, "block_ip": "***********", "location": ["印度"], "action": "阻断", "conditions": ["外网恶意攻击"], "type": "流量检测", "create_time": "2025-08-20 12:44:55", "release_time": "2025-08-27 12:46:57", "remarks": ""}]

2025-08-26 15:39:29.352 [Thread-7] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:39:29.353 [Thread-6] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 79毫秒
2025-08-26 15:39:29.354 [Thread-7] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：5,等待任务: 0
2025-08-26 15:39:29.356 [async-task-pool58] INFO  c.r.f.a.e.PullFlowRiskAssetsEvent - [lambda$1,100] - 开始获取流量风险资产数据: 测试非凡设备1
2025-08-26 15:39:29.356 [async-task-pool54] INFO  c.r.f.a.e.PullFlowRiskAssetsEvent - [lambda$1,100] - 开始获取流量风险资产数据: 测试设备2
2025-08-26 15:39:29.360 [async-task-pool58] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,352] - 开始拉取流量风险资产数据，参数：FlowRiskAssetsParam(accessToken=null, riskType=all, startTime=2025-08-25 15:37:27, endTime=2025-08-26 15:37:29, page=1, pageSize=30, deviceConfigId=1)
2025-08-26 15:39:29.360 [async-task-pool54] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,352] - 开始拉取流量风险资产数据，参数：FlowRiskAssetsParam(accessToken=null, riskType=all, startTime=2025-08-25 15:37:27, endTime=2025-08-26 15:37:29, page=1, pageSize=30, deviceConfigId=4)
2025-08-26 15:39:29.362 [async-task-pool58] INFO  c.r.f.a.d.FlowRiskAssetsParam - [getRequestBase,49] - 非凡API流量风险资产请求参数: https://***************:23000/v2/flow-risk-assets?access_token=lst2BAzxJTQL0eSZ&risk_type=all&start_date=2025-08-25%2015%3A37%3A27&end_date=2025-08-26%2015%3A37%3A29&page=1&page_size=30
2025-08-26 15:39:29.362 [async-task-pool54] INFO  c.r.f.a.d.FlowRiskAssetsParam - [getRequestBase,49] - 非凡API流量风险资产请求参数: https://***************:23000/v2/flow-risk-assets?access_token=lst2BAzxJTQL0eSZ&risk_type=all&start_date=2025-08-25%2015%3A37%3A27&end_date=2025-08-26%2015%3A37%3A29&page=1&page_size=30
2025-08-26 15:39:29.363 [async-task-pool61] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,987] - 主机入侵攻击总数: 0
2025-08-26 15:39:29.366 [async-task-pool61] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1010] - 第1页获取0条数据
2025-08-26 15:39:29.367 [async-task-pool61] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1014] - 当前页无数据，停止获取
2025-08-26 15:39:29.367 [async-task-pool61] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1083] - 拉取主机入侵攻击数据成功，但没有有效数据
2025-08-26 15:39:29.373 [async-task-pool181] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,987] - 主机入侵攻击总数: 0
2025-08-26 15:39:29.374 [async-task-pool181] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1010] - 第1页获取0条数据
2025-08-26 15:39:29.375 [async-task-pool181] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1014] - 当前页无数据，停止获取
2025-08-26 15:39:29.376 [async-task-pool181] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1083] - 拉取主机入侵攻击数据成功，但没有有效数据
2025-08-26 15:39:29.388 [taskScheduler-31] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 102毫秒
2025-08-26 15:39:29.391 [Thread-8] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 98毫秒
2025-08-26 15:39:29.442 [async-task-pool58] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,458] - 流量风险资产总数: 6
2025-08-26 15:39:29.442 [async-task-pool58] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,481] - 第1页获取6条数据
2025-08-26 15:39:29.443 [async-task-pool58] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,488] - 当前页数据量(6)少于页大小(30)，已获取完所有数据
2025-08-26 15:39:29.444 [async-task-pool54] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,458] - 流量风险资产总数: 6
2025-08-26 15:39:29.444 [async-task-pool54] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,481] - 第1页获取6条数据
2025-08-26 15:39:29.445 [async-task-pool54] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,488] - 当前页数据量(6)少于页大小(30)，已获取完所有数据
2025-08-26 15:39:29.469 [async-task-pool58] INFO  c.r.m.c.c.FfsafeClientService - [processBatch,662] - 批量更新6条流量风险资产数据
2025-08-26 15:39:29.470 [async-task-pool58] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,551] - 拉取流量风险资产数据完成，耗时110ms，共1页，共6条数据，处理结果: true
2025-08-26 15:39:29.477 [async-task-pool54] INFO  c.r.m.c.c.FfsafeClientService - [processBatch,662] - 批量更新6条流量风险资产数据
2025-08-26 15:39:29.478 [async-task-pool54] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,551] - 拉取流量风险资产数据完成，耗时118ms，共1页，共6条数据，处理结果: true
2025-08-26 15:39:29.495 [Thread-7] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 143毫秒
2025-08-26 15:39:29.951 [pool-4-thread-1] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [lambda$0,81] - 最大连接：200 当前连接: 2
2025-08-26 15:39:34.398 [Thread-5] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:39:34.399 [Thread-5] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：1,等待任务: 0
2025-08-26 15:39:34.399 [async-task-pool68] INFO  c.r.f.a.e.PullDataEvent - [lambda$1,102] - 开始流量告警日志同步: 测试非凡设备1
2025-08-26 15:39:34.399 [async-task-pool57] INFO  c.r.f.a.e.PullDataEvent - [lambda$1,102] - 开始流量告警日志同步: 测试设备2
2025-08-26 15:39:34.534 [taskScheduler-45] INFO  c.r.r.s.HandleDataSyncSender - [sendDataSync,230] - 数据同步发送成功, messageId: ffc7809a-651a-4e83-9475-23ad95a0577f, 加密状态: 已加密, 消息类型: PING
2025-08-26 15:39:34.536 [taskScheduler-45] INFO  c.r.r.t.RabbitMQTask - [ping,44] - 发送心跳消息
2025-08-26 15:39:34.541 [rabbitConnectionFactory9] INFO  c.r.f.c.r.RabbitMQConfig - [lambda$0,290] - 消息已成功发送到交换机, messageId: ffc7809a-651a-4e83-9475-23ad95a0577f
2025-08-26 15:39:34.629 [Thread-5] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 231毫秒
2025-08-26 15:39:34.967 [taskScheduler-5] INFO  c.r.c.task.HlsTask - [checkHls,65] - hls检查开始...
2025-08-26 15:39:34.971 [taskScheduler-5] INFO  c.r.c.task.HlsTask - [checkHls,86] - hls检查结束...
2025-08-26 15:39:39.364 [Thread-6] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:39:39.365 [Thread-6] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：1,等待任务: 0
2025-08-26 15:39:39.365 [async-task-pool10] INFO  c.r.f.a.e.PullFilterLogEvent - [lambda$1,90] - 开始获取IP阻断日志: 测试设备2
2025-08-26 15:39:39.365 [async-task-pool45] INFO  c.r.f.a.e.PullFilterLogEvent - [lambda$1,90] - 开始获取IP阻断日志: 测试非凡设备1
2025-08-26 15:39:39.402 [taskScheduler-9] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:39:39.402 [taskScheduler-9] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：3,等待任务: 0
2025-08-26 15:39:39.409 [Thread-8] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:39:39.409 [Thread-8] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：5,等待任务: 0
2025-08-26 15:39:39.411 [async-task-pool44] INFO  c.r.f.a.e.PullHostIntrusionAttackEvent - [lambda$1,83] - 开始获取主机入侵攻击数据: 测试设备2
2025-08-26 15:39:39.411 [async-task-pool33] INFO  c.r.f.a.e.PullHostIntrusionAttackEvent - [lambda$1,83] - 开始获取主机入侵攻击数据: 测试非凡设备1
2025-08-26 15:39:39.412 [async-task-pool44] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,874] - 开始拉取主机入侵攻击数据，参数：HostIntrusionAttackParam(accessToken=null, sip=null, dip=null, startDate=2025-08-25 15:37:29, endDate=2025-08-26 15:37:39, page=1, pSize=20, deviceConfigId=4)
2025-08-26 15:39:39.412 [async-task-pool33] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,874] - 开始拉取主机入侵攻击数据，参数：HostIntrusionAttackParam(accessToken=null, sip=null, dip=null, startDate=2025-08-25 15:37:29, endDate=2025-08-26 15:37:39, page=1, pSize=20, deviceConfigId=1)
2025-08-26 15:39:39.413 [async-task-pool44] INFO  c.r.f.a.d.HostIntrusionAttackParam - [getRequestBase,80] - 非凡API主机入侵攻击分页请求参数: https://***************:23000/v2/host-edr-intrusion?access_token=lst2BAzxJTQL0eSZ&start_date=2025-08-25%2015%3A37%3A29&end_date=2025-08-26%2015%3A37%3A39&page=1&p_size=20
2025-08-26 15:39:39.416 [async-task-pool33] INFO  c.r.f.a.d.HostIntrusionAttackParam - [getRequestBase,80] - 非凡API主机入侵攻击分页请求参数: https://***************:23000/v2/host-edr-intrusion?access_token=lst2BAzxJTQL0eSZ&start_date=2025-08-25%2015%3A37%3A29&end_date=2025-08-26%2015%3A37%3A39&page=1&p_size=20
2025-08-26 15:39:39.479 [async-task-pool46] INFO  c.r.f.c.FFSafeRequestComponent - [execRequest,88] - 非凡请求响应: [{"id": 1058, "block_ip": "*************", "location": ["美国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-22 00:58:57", "release_time": "2025-08-29 01:00:59", "remarks": ""}, {"id": 1057, "block_ip": "************", "location": ["法国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-21 13:55:52", "release_time": "2025-08-28 13:56:53", "remarks": ""}, {"id": 1056, "block_ip": "***********", "location": ["印度"], "action": "阻断", "conditions": ["外网恶意攻击"], "type": "流量检测", "create_time": "2025-08-20 12:44:55", "release_time": "2025-08-27 12:46:57", "remarks": ""}]

2025-08-26 15:39:39.479 [async-task-pool73] INFO  c.r.f.c.FFSafeRequestComponent - [execRequest,88] - 非凡请求响应: [{"id": 1058, "block_ip": "*************", "location": ["美国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-22 00:58:57", "release_time": "2025-08-29 01:00:59", "remarks": ""}, {"id": 1057, "block_ip": "************", "location": ["法国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-21 13:55:52", "release_time": "2025-08-28 13:56:53", "remarks": ""}, {"id": 1056, "block_ip": "***********", "location": ["印度"], "action": "阻断", "conditions": ["外网恶意攻击"], "type": "流量检测", "create_time": "2025-08-20 12:44:55", "release_time": "2025-08-27 12:46:57", "remarks": ""}]

2025-08-26 15:39:39.486 [async-task-pool33] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,987] - 主机入侵攻击总数: 0
2025-08-26 15:39:39.486 [async-task-pool33] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1010] - 第1页获取0条数据
2025-08-26 15:39:39.487 [async-task-pool33] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1014] - 当前页无数据，停止获取
2025-08-26 15:39:39.488 [async-task-pool33] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1083] - 拉取主机入侵攻击数据成功，但没有有效数据
2025-08-26 15:39:39.495 [taskScheduler-9] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 93毫秒
2025-08-26 15:39:39.499 [Thread-6] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 135毫秒
2025-08-26 15:39:39.500 [Thread-7] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:39:39.500 [Thread-7] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：2,等待任务: 0
2025-08-26 15:39:39.501 [async-task-pool53] INFO  c.r.f.a.e.PullFlowRiskAssetsEvent - [lambda$1,100] - 开始获取流量风险资产数据: 测试非凡设备1
2025-08-26 15:39:39.501 [async-task-pool52] INFO  c.r.f.a.e.PullFlowRiskAssetsEvent - [lambda$1,100] - 开始获取流量风险资产数据: 测试设备2
2025-08-26 15:39:39.501 [async-task-pool53] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,352] - 开始拉取流量风险资产数据，参数：FlowRiskAssetsParam(accessToken=null, riskType=all, startTime=2025-08-25 15:37:37, endTime=2025-08-26 15:37:39, page=1, pageSize=30, deviceConfigId=1)
2025-08-26 15:39:39.501 [async-task-pool52] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,352] - 开始拉取流量风险资产数据，参数：FlowRiskAssetsParam(accessToken=null, riskType=all, startTime=2025-08-25 15:37:37, endTime=2025-08-26 15:37:39, page=1, pageSize=30, deviceConfigId=4)
2025-08-26 15:39:39.502 [async-task-pool53] INFO  c.r.f.a.d.FlowRiskAssetsParam - [getRequestBase,49] - 非凡API流量风险资产请求参数: https://***************:23000/v2/flow-risk-assets?access_token=lst2BAzxJTQL0eSZ&risk_type=all&start_date=2025-08-25%2015%3A37%3A37&end_date=2025-08-26%2015%3A37%3A39&page=1&page_size=30
2025-08-26 15:39:39.503 [async-task-pool52] INFO  c.r.f.a.d.FlowRiskAssetsParam - [getRequestBase,49] - 非凡API流量风险资产请求参数: https://***************:23000/v2/flow-risk-assets?access_token=lst2BAzxJTQL0eSZ&risk_type=all&start_date=2025-08-25%2015%3A37%3A37&end_date=2025-08-26%2015%3A37%3A39&page=1&page_size=30
2025-08-26 15:39:39.511 [async-task-pool44] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,987] - 主机入侵攻击总数: 0
2025-08-26 15:39:39.511 [async-task-pool44] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1010] - 第1页获取0条数据
2025-08-26 15:39:39.512 [async-task-pool44] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1014] - 当前页无数据，停止获取
2025-08-26 15:39:39.513 [async-task-pool44] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1083] - 拉取主机入侵攻击数据成功，但没有有效数据
2025-08-26 15:39:39.525 [Thread-8] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 116毫秒
2025-08-26 15:39:39.576 [async-task-pool53] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,458] - 流量风险资产总数: 6
2025-08-26 15:39:39.577 [async-task-pool53] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,481] - 第1页获取6条数据
2025-08-26 15:39:39.577 [async-task-pool53] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,488] - 当前页数据量(6)少于页大小(30)，已获取完所有数据
2025-08-26 15:39:39.589 [async-task-pool52] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,458] - 流量风险资产总数: 6
2025-08-26 15:39:39.595 [async-task-pool52] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,481] - 第1页获取6条数据
2025-08-26 15:39:39.595 [async-task-pool52] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,488] - 当前页数据量(6)少于页大小(30)，已获取完所有数据
2025-08-26 15:39:39.599 [async-task-pool53] INFO  c.r.m.c.c.FfsafeClientService - [processBatch,662] - 批量更新6条流量风险资产数据
2025-08-26 15:39:39.599 [async-task-pool53] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,551] - 拉取流量风险资产数据完成，耗时98ms，共1页，共6条数据，处理结果: true
2025-08-26 15:39:39.614 [async-task-pool52] INFO  c.r.m.c.c.FfsafeClientService - [processBatch,662] - 批量更新6条流量风险资产数据
2025-08-26 15:39:39.615 [async-task-pool52] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,551] - 拉取流量风险资产数据完成，耗时114ms，共1页，共6条数据，处理结果: true
2025-08-26 15:39:39.627 [Thread-7] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 127毫秒
2025-08-26 15:39:39.952 [pool-4-thread-1] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [lambda$0,81] - 最大连接：200 当前连接: 2
2025-08-26 15:39:43.523 [pool-6-thread-5] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 15秒519毫秒
2025-08-26 15:39:44.070 [pool-6-thread-5] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sync,255] - 批量更新攻击者视角告警列开始: 9
2025-08-26 15:39:44.114 [pool-6-thread-5] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=***************, operationType=UPDATE
2025-08-26 15:39:44.117 [pool-6-thread-5] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=***********, operationType=UPDATE
2025-08-26 15:39:44.121 [pool-6-thread-5] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=**************, operationType=UPDATE
2025-08-26 15:39:44.122 [pool-6-thread-5] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=***********, operationType=UPDATE
2025-08-26 15:39:44.129 [pool-6-thread-5] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=***********, operationType=UPDATE
2025-08-26 15:39:44.131 [pool-6-thread-5] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=**********, operationType=UPDATE
2025-08-26 15:39:44.133 [pool-6-thread-5] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=***********, operationType=UPDATE
2025-08-26 15:39:44.136 [pool-6-thread-5] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=**************, operationType=UPDATE
2025-08-26 15:39:44.138 [pool-6-thread-5] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=*************, operationType=UPDATE
2025-08-26 15:39:44.142 [pool-6-thread-2] INFO  c.r.s.t.FfSafeFlowAlarmTask - [syncTask,46] - 开始同步攻击者告警数据
2025-08-26 15:39:44.213 [pool-6-thread-2] INFO  c.r.f.c.FFSafeRequestComponent - [execRequest,88] - 非凡请求响应: [{"attack_ip": "***************", "risk_level": 3, "ip_tags": ["内网端口扫描", "内网恶意攻击", "暴力破解"], "location": "局域网", "victim_ip_nums": 30, "attack_type_nums": 1079, "attack_nums": 58440, "start_time": "2025-08-26 14:58:22", "update_time": "2025-08-26 15:34:10"}, {"attack_ip": "***********", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 2, "attack_type_nums": 2, "attack_nums": 155, "start_time": "2025-08-25 13:27:18", "update_time": "2025-08-26 15:33:16"}, {"attack_ip": "**************", "risk_level": 3, "ip_tags": ["内网端口扫描", "内网恶意攻击", "暴力破解"], "location": "局域网", "victim_ip_nums": 257, "attack_type_nums": 62, "attack_nums": 1176, "start_time": "2025-08-26 14:24:32", "update_time": "2025-08-26 15:32:50"}, {"attack_ip": "***********", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 1, "attack_type_nums": 3, "attack_nums": 106, "start_time": "2025-08-26 09:49:05", "update_time": "2025-08-26 15:31:50"}, {"attack_ip": "***********", "risk_level": 2, "ip_tags": ["踩中蜜罐", "内网可疑行为"], "location": "局域网", "victim_ip_nums": 2, "attack_type_nums": 6, "attack_nums": 115, "start_time": "2025-08-25 14:42:32", "update_time": "2025-08-26 15:30:35"}, {"attack_ip": "**********", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 1, "attack_type_nums": 1, "attack_nums": 403, "start_time": "2025-08-25 08:14:50", "update_time": "2025-08-26 15:29:54"}, {"attack_ip": "***********", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 1, "attack_type_nums": 1, "attack_nums": 277, "start_time": "2025-08-25 08:27:55", "update_time": "2025-08-26 15:29:41"}, {"attack_ip": "**************", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 1, "attack_type_nums": 1, "attack_nums": 13, "start_time": "2025-08-26 11:06:34", "update_time": "2025-08-26 15:28:37"}, {"attack_ip": "*************", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 2, "attack_type_nums": 1, "attack_nums": 331, "start_time": "2025-08-25 08:03:53", "update_time": "2025-08-26 15:27:58"}, {"attack_ip": "*************", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 2, "attack_type_nums": 1, "attack_nums": 321, "start_time": "2025-08-25 08:03:15", "update_time": "2025-08-26 15:27:20"}]

2025-08-26 15:39:44.216 [pool-6-thread-2] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:10...
2025-08-26 15:39:44.217 [pool-6-thread-2] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：0,等待任务: 0
2025-08-26 15:39:44.633 [Thread-5] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:39:44.633 [Thread-5] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：1,等待任务: 0
2025-08-26 15:39:44.634 [async-task-pool76] INFO  c.r.f.a.e.PullDataEvent - [lambda$1,102] - 开始流量告警日志同步: 测试非凡设备1
2025-08-26 15:39:44.634 [async-task-pool95] INFO  c.r.f.a.e.PullDataEvent - [lambda$1,102] - 开始流量告警日志同步: 测试设备2
2025-08-26 15:39:45.298 [Thread-5] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 665毫秒
2025-08-26 15:39:49.601 [Thread-8] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:39:49.602 [taskScheduler-9] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:39:49.602 [Thread-6] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:39:49.603 [Thread-8] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：1,等待任务: 0
2025-08-26 15:39:49.604 [taskScheduler-9] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：1,等待任务: 0
2025-08-26 15:39:49.604 [Thread-6] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：1,等待任务: 0
2025-08-26 15:39:49.604 [async-task-pool89] INFO  c.r.f.a.e.PullHostIntrusionAttackEvent - [lambda$1,83] - 开始获取主机入侵攻击数据: 测试非凡设备1
2025-08-26 15:39:49.604 [async-task-pool85] INFO  c.r.f.a.e.PullHostIntrusionAttackEvent - [lambda$1,83] - 开始获取主机入侵攻击数据: 测试设备2
2025-08-26 15:39:49.605 [async-task-pool31] INFO  c.r.f.a.e.PullFilterLogEvent - [lambda$1,90] - 开始获取IP阻断日志: 测试非凡设备1
2025-08-26 15:39:49.607 [async-task-pool85] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,874] - 开始拉取主机入侵攻击数据，参数：HostIntrusionAttackParam(accessToken=null, sip=null, dip=null, startDate=2025-08-25 15:37:39, endDate=2025-08-26 15:37:49, page=1, pSize=20, deviceConfigId=4)
2025-08-26 15:39:49.605 [async-task-pool105] INFO  c.r.f.a.e.PullFilterLogEvent - [lambda$1,90] - 开始获取IP阻断日志: 测试设备2
2025-08-26 15:39:49.606 [async-task-pool89] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,874] - 开始拉取主机入侵攻击数据，参数：HostIntrusionAttackParam(accessToken=null, sip=null, dip=null, startDate=2025-08-25 15:37:39, endDate=2025-08-26 15:37:49, page=1, pSize=20, deviceConfigId=1)
2025-08-26 15:39:49.608 [async-task-pool85] INFO  c.r.f.a.d.HostIntrusionAttackParam - [getRequestBase,80] - 非凡API主机入侵攻击分页请求参数: https://***************:23000/v2/host-edr-intrusion?access_token=lst2BAzxJTQL0eSZ&start_date=2025-08-25%2015%3A37%3A39&end_date=2025-08-26%2015%3A37%3A49&page=1&p_size=20
2025-08-26 15:39:49.610 [async-task-pool89] INFO  c.r.f.a.d.HostIntrusionAttackParam - [getRequestBase,80] - 非凡API主机入侵攻击分页请求参数: https://***************:23000/v2/host-edr-intrusion?access_token=lst2BAzxJTQL0eSZ&start_date=2025-08-25%2015%3A37%3A39&end_date=2025-08-26%2015%3A37%3A49&page=1&p_size=20
2025-08-26 15:39:49.643 [Thread-7] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:39:49.643 [Thread-7] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：7,等待任务: 0
2025-08-26 15:39:49.644 [async-task-pool77] INFO  c.r.f.a.e.PullFlowRiskAssetsEvent - [lambda$1,100] - 开始获取流量风险资产数据: 测试设备2
2025-08-26 15:39:49.644 [async-task-pool87] INFO  c.r.f.a.e.PullFlowRiskAssetsEvent - [lambda$1,100] - 开始获取流量风险资产数据: 测试非凡设备1
2025-08-26 15:39:49.644 [async-task-pool77] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,352] - 开始拉取流量风险资产数据，参数：FlowRiskAssetsParam(accessToken=null, riskType=all, startTime=2025-08-25 15:37:47, endTime=2025-08-26 15:37:49, page=1, pageSize=30, deviceConfigId=4)
2025-08-26 15:39:49.644 [async-task-pool87] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,352] - 开始拉取流量风险资产数据，参数：FlowRiskAssetsParam(accessToken=null, riskType=all, startTime=2025-08-25 15:37:47, endTime=2025-08-26 15:37:49, page=1, pageSize=30, deviceConfigId=1)
2025-08-26 15:39:49.645 [async-task-pool77] INFO  c.r.f.a.d.FlowRiskAssetsParam - [getRequestBase,49] - 非凡API流量风险资产请求参数: https://***************:23000/v2/flow-risk-assets?access_token=lst2BAzxJTQL0eSZ&risk_type=all&start_date=2025-08-25%2015%3A37%3A47&end_date=2025-08-26%2015%3A37%3A49&page=1&page_size=30
2025-08-26 15:39:49.645 [async-task-pool87] INFO  c.r.f.a.d.FlowRiskAssetsParam - [getRequestBase,49] - 非凡API流量风险资产请求参数: https://***************:23000/v2/flow-risk-assets?access_token=lst2BAzxJTQL0eSZ&risk_type=all&start_date=2025-08-25%2015%3A37%3A47&end_date=2025-08-26%2015%3A37%3A49&page=1&page_size=30
2025-08-26 15:39:49.669 [async-task-pool19] INFO  c.r.f.c.FFSafeRequestComponent - [execRequest,88] - 非凡请求响应: [{"id": 1058, "block_ip": "*************", "location": ["美国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-22 00:58:57", "release_time": "2025-08-29 01:00:59", "remarks": ""}, {"id": 1057, "block_ip": "************", "location": ["法国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-21 13:55:52", "release_time": "2025-08-28 13:56:53", "remarks": ""}, {"id": 1056, "block_ip": "***********", "location": ["印度"], "action": "阻断", "conditions": ["外网恶意攻击"], "type": "流量检测", "create_time": "2025-08-20 12:44:55", "release_time": "2025-08-27 12:46:57", "remarks": ""}]

2025-08-26 15:39:49.688 [async-task-pool91] INFO  c.r.f.c.FFSafeRequestComponent - [execRequest,88] - 非凡请求响应: [{"id": 1058, "block_ip": "*************", "location": ["美国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-22 00:58:57", "release_time": "2025-08-29 01:00:59", "remarks": ""}, {"id": 1057, "block_ip": "************", "location": ["法国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-21 13:55:52", "release_time": "2025-08-28 13:56:53", "remarks": ""}, {"id": 1056, "block_ip": "***********", "location": ["印度"], "action": "阻断", "conditions": ["外网恶意攻击"], "type": "流量检测", "create_time": "2025-08-20 12:44:55", "release_time": "2025-08-27 12:46:57", "remarks": ""}]

2025-08-26 15:39:49.695 [Thread-6] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 93毫秒
2025-08-26 15:39:49.698 [taskScheduler-9] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 96毫秒
2025-08-26 15:39:49.701 [async-task-pool89] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,987] - 主机入侵攻击总数: 0
2025-08-26 15:39:49.701 [async-task-pool89] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1010] - 第1页获取0条数据
2025-08-26 15:39:49.701 [async-task-pool89] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1014] - 当前页无数据，停止获取
2025-08-26 15:39:49.701 [async-task-pool89] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1083] - 拉取主机入侵攻击数据成功，但没有有效数据
2025-08-26 15:39:49.715 [async-task-pool85] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,987] - 主机入侵攻击总数: 0
2025-08-26 15:39:49.716 [async-task-pool85] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1010] - 第1页获取0条数据
2025-08-26 15:39:49.716 [async-task-pool85] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1014] - 当前页无数据，停止获取
2025-08-26 15:39:49.721 [async-task-pool85] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1083] - 拉取主机入侵攻击数据成功，但没有有效数据
2025-08-26 15:39:49.738 [async-task-pool77] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,458] - 流量风险资产总数: 6
2025-08-26 15:39:49.740 [async-task-pool77] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,481] - 第1页获取6条数据
2025-08-26 15:39:49.742 [async-task-pool77] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,488] - 当前页数据量(6)少于页大小(30)，已获取完所有数据
2025-08-26 15:39:49.742 [Thread-8] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 141毫秒
2025-08-26 15:39:49.752 [async-task-pool87] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,458] - 流量风险资产总数: 6
2025-08-26 15:39:49.758 [async-task-pool87] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,481] - 第1页获取6条数据
2025-08-26 15:39:49.767 [async-task-pool87] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,488] - 当前页数据量(6)少于页大小(30)，已获取完所有数据
2025-08-26 15:39:49.848 [async-task-pool77] INFO  c.r.m.c.c.FfsafeClientService - [processBatch,662] - 批量更新6条流量风险资产数据
2025-08-26 15:39:49.850 [async-task-pool77] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,551] - 拉取流量风险资产数据完成，耗时206ms，共1页，共6条数据，处理结果: true
2025-08-26 15:39:49.854 [async-task-pool87] INFO  c.r.m.c.c.FfsafeClientService - [processBatch,662] - 批量更新6条流量风险资产数据
2025-08-26 15:39:49.854 [async-task-pool87] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,551] - 拉取流量风险资产数据完成，耗时210ms，共1页，共6条数据，处理结果: true
2025-08-26 15:39:49.886 [Thread-7] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 243毫秒
2025-08-26 15:39:49.954 [pool-4-thread-1] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [lambda$0,81] - 最大连接：200 当前连接: 2
2025-08-26 15:39:55.696 [Thread-5] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:39:55.697 [Thread-5] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：1,等待任务: 0
2025-08-26 15:39:55.698 [async-task-pool92] INFO  c.r.f.a.e.PullDataEvent - [lambda$1,102] - 开始流量告警日志同步: 测试非凡设备1
2025-08-26 15:39:55.698 [async-task-pool51] INFO  c.r.f.a.e.PullDataEvent - [lambda$1,102] - 开始流量告警日志同步: 测试设备2
2025-08-26 15:39:56.083 [Thread-5] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 387毫秒
2025-08-26 15:39:57.824 [pool-6-thread-2] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 13秒608毫秒
2025-08-26 15:39:58.520 [pool-6-thread-2] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sync,255] - 批量更新攻击者视角告警列开始: 10
2025-08-26 15:39:58.550 [pool-6-thread-2] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=***************, operationType=UPDATE
2025-08-26 15:39:58.552 [pool-6-thread-2] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=***********, operationType=UPDATE
2025-08-26 15:39:58.557 [pool-6-thread-2] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=**************, operationType=UPDATE
2025-08-26 15:39:58.559 [pool-6-thread-2] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=***********, operationType=UPDATE
2025-08-26 15:39:58.562 [pool-6-thread-2] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=***********, operationType=UPDATE
2025-08-26 15:39:58.565 [pool-6-thread-2] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=**********, operationType=UPDATE
2025-08-26 15:39:58.567 [pool-6-thread-2] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=***********, operationType=UPDATE
2025-08-26 15:39:58.573 [pool-6-thread-2] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=**************, operationType=UPDATE
2025-08-26 15:39:58.576 [pool-6-thread-2] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=*************, operationType=UPDATE
2025-08-26 15:39:58.577 [pool-6-thread-2] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=*************, operationType=UPDATE
2025-08-26 15:39:58.586 [pool-6-thread-5] INFO  c.r.s.t.FfSafeFlowAlarmTask - [syncTask,46] - 开始同步攻击者告警数据
2025-08-26 15:39:58.648 [pool-6-thread-5] INFO  c.r.f.c.FFSafeRequestComponent - [execRequest,88] - 非凡请求响应: [{"attack_ip": "*************", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 2, "attack_type_nums": 1, "attack_nums": 336, "start_time": "2025-08-25 08:02:29", "update_time": "2025-08-26 15:38:37"}, {"attack_ip": "**********0", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 1, "attack_type_nums": 1, "attack_nums": 584, "start_time": "2025-08-25 08:59:16", "update_time": "2025-08-26 15:38:31"}, {"attack_ip": "************", "risk_level": 3, "ip_tags": ["内网恶意攻击"], "location": "局域网", "victim_ip_nums": 5, "attack_type_nums": 6, "attack_nums": 97, "start_time": "2025-08-25 16:17:50", "update_time": "2025-08-26 15:37:44"}, {"attack_ip": "***********", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 1, "attack_type_nums": 1, "attack_nums": 239, "start_time": "2025-08-26 09:54:16", "update_time": "2025-08-26 15:37:42"}, {"attack_ip": "***************", "risk_level": 3, "ip_tags": ["内网端口扫描", "内网恶意攻击", "暴力破解"], "location": "局域网", "victim_ip_nums": 30, "attack_type_nums": 1079, "attack_nums": 58440, "start_time": "2025-08-26 14:58:22", "update_time": "2025-08-26 15:34:10"}, {"attack_ip": "***********", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 2, "attack_type_nums": 2, "attack_nums": 155, "start_time": "2025-08-25 13:27:18", "update_time": "2025-08-26 15:33:16"}, {"attack_ip": "**************", "risk_level": 3, "ip_tags": ["内网端口扫描", "内网恶意攻击", "暴力破解"], "location": "局域网", "victim_ip_nums": 257, "attack_type_nums": 62, "attack_nums": 1176, "start_time": "2025-08-26 14:24:32", "update_time": "2025-08-26 15:32:50"}, {"attack_ip": "***********", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 1, "attack_type_nums": 3, "attack_nums": 106, "start_time": "2025-08-26 09:49:05", "update_time": "2025-08-26 15:31:50"}, {"attack_ip": "***********", "risk_level": 2, "ip_tags": ["踩中蜜罐", "内网可疑行为"], "location": "局域网", "victim_ip_nums": 2, "attack_type_nums": 6, "attack_nums": 115, "start_time": "2025-08-25 14:42:32", "update_time": "2025-08-26 15:30:35"}, {"attack_ip": "**********", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 1, "attack_type_nums": 1, "attack_nums": 403, "start_time": "2025-08-25 08:14:50", "update_time": "2025-08-26 15:29:54"}, {"attack_ip": "***********", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 1, "attack_type_nums": 1, "attack_nums": 277, "start_time": "2025-08-25 08:27:55", "update_time": "2025-08-26 15:29:41"}]

2025-08-26 15:39:58.652 [pool-6-thread-5] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:11...
2025-08-26 15:39:58.653 [pool-6-thread-5] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：0,等待任务: 0
2025-08-26 15:39:59.701 [Thread-6] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:39:59.702 [Thread-6] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：1,等待任务: 0
2025-08-26 15:39:59.705 [async-task-pool116] INFO  c.r.f.a.e.PullFilterLogEvent - [lambda$1,90] - 开始获取IP阻断日志: 测试非凡设备1
2025-08-26 15:39:59.705 [async-task-pool72] INFO  c.r.f.a.e.PullFilterLogEvent - [lambda$1,90] - 开始获取IP阻断日志: 测试设备2
2025-08-26 15:39:59.711 [taskScheduler-9] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:39:59.714 [taskScheduler-9] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：3,等待任务: 0
2025-08-26 15:39:59.758 [async-task-pool84] INFO  c.r.f.c.FFSafeRequestComponent - [execRequest,88] - 非凡请求响应: [{"id": 1058, "block_ip": "*************", "location": ["美国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-22 00:58:57", "release_time": "2025-08-29 01:00:59", "remarks": ""}, {"id": 1057, "block_ip": "************", "location": ["法国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-21 13:55:52", "release_time": "2025-08-28 13:56:53", "remarks": ""}, {"id": 1056, "block_ip": "***********", "location": ["印度"], "action": "阻断", "conditions": ["外网恶意攻击"], "type": "流量检测", "create_time": "2025-08-20 12:44:55", "release_time": "2025-08-27 12:46:57", "remarks": ""}]

2025-08-26 15:39:59.770 [Thread-8] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:39:59.771 [Thread-8] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：5,等待任务: 0
2025-08-26 15:39:59.771 [async-task-pool100] INFO  c.r.f.a.e.PullHostIntrusionAttackEvent - [lambda$1,83] - 开始获取主机入侵攻击数据: 测试非凡设备1
2025-08-26 15:39:59.771 [async-task-pool71] INFO  c.r.f.a.e.PullHostIntrusionAttackEvent - [lambda$1,83] - 开始获取主机入侵攻击数据: 测试设备2
2025-08-26 15:39:59.771 [async-task-pool100] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,874] - 开始拉取主机入侵攻击数据，参数：HostIntrusionAttackParam(accessToken=null, sip=null, dip=null, startDate=2025-08-25 15:37:49, endDate=2025-08-26 15:37:59, page=1, pSize=20, deviceConfigId=1)
2025-08-26 15:39:59.771 [async-task-pool71] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,874] - 开始拉取主机入侵攻击数据，参数：HostIntrusionAttackParam(accessToken=null, sip=null, dip=null, startDate=2025-08-25 15:37:49, endDate=2025-08-26 15:37:59, page=1, pSize=20, deviceConfigId=4)
2025-08-26 15:39:59.777 [async-task-pool100] INFO  c.r.f.a.d.HostIntrusionAttackParam - [getRequestBase,80] - 非凡API主机入侵攻击分页请求参数: https://***************:23000/v2/host-edr-intrusion?access_token=lst2BAzxJTQL0eSZ&start_date=2025-08-25%2015%3A37%3A49&end_date=2025-08-26%2015%3A37%3A59&page=1&p_size=20
2025-08-26 15:39:59.777 [async-task-pool71] INFO  c.r.f.a.d.HostIntrusionAttackParam - [getRequestBase,80] - 非凡API主机入侵攻击分页请求参数: https://***************:23000/v2/host-edr-intrusion?access_token=lst2BAzxJTQL0eSZ&start_date=2025-08-25%2015%3A37%3A49&end_date=2025-08-26%2015%3A37%3A59&page=1&p_size=20
2025-08-26 15:39:59.842 [async-task-pool106] INFO  c.r.f.c.FFSafeRequestComponent - [execRequest,88] - 非凡请求响应: [{"id": 1058, "block_ip": "*************", "location": ["美国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-22 00:58:57", "release_time": "2025-08-29 01:00:59", "remarks": ""}, {"id": 1057, "block_ip": "************", "location": ["法国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-21 13:55:52", "release_time": "2025-08-28 13:56:53", "remarks": ""}, {"id": 1056, "block_ip": "***********", "location": ["印度"], "action": "阻断", "conditions": ["外网恶意攻击"], "type": "流量检测", "create_time": "2025-08-20 12:44:55", "release_time": "2025-08-27 12:46:57", "remarks": ""}]

2025-08-26 15:39:59.859 [async-task-pool100] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,987] - 主机入侵攻击总数: 0
2025-08-26 15:39:59.863 [async-task-pool71] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,987] - 主机入侵攻击总数: 0
2025-08-26 15:39:59.865 [async-task-pool100] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1010] - 第1页获取0条数据
2025-08-26 15:39:59.868 [async-task-pool71] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1010] - 第1页获取0条数据
2025-08-26 15:39:59.869 [async-task-pool100] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1014] - 当前页无数据，停止获取
2025-08-26 15:39:59.870 [async-task-pool71] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1014] - 当前页无数据，停止获取
2025-08-26 15:39:59.871 [async-task-pool100] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1083] - 拉取主机入侵攻击数据成功，但没有有效数据
2025-08-26 15:39:59.872 [async-task-pool71] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1083] - 拉取主机入侵攻击数据成功，但没有有效数据
2025-08-26 15:39:59.903 [taskScheduler-9] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 192毫秒
2025-08-26 15:39:59.908 [Thread-6] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 207毫秒
2025-08-26 15:39:59.910 [Thread-7] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:39:59.911 [Thread-7] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：2,等待任务: 0
2025-08-26 15:39:59.913 [Thread-8] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 143毫秒
2025-08-26 15:39:59.918 [async-task-pool14] INFO  c.r.f.a.e.PullFlowRiskAssetsEvent - [lambda$1,100] - 开始获取流量风险资产数据: 测试设备2
2025-08-26 15:39:59.918 [async-task-pool101] INFO  c.r.f.a.e.PullFlowRiskAssetsEvent - [lambda$1,100] - 开始获取流量风险资产数据: 测试非凡设备1
2025-08-26 15:39:59.921 [async-task-pool14] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,352] - 开始拉取流量风险资产数据，参数：FlowRiskAssetsParam(accessToken=null, riskType=all, startTime=2025-08-25 15:37:57, endTime=2025-08-26 15:37:59, page=1, pageSize=30, deviceConfigId=4)
2025-08-26 15:39:59.922 [async-task-pool101] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,352] - 开始拉取流量风险资产数据，参数：FlowRiskAssetsParam(accessToken=null, riskType=all, startTime=2025-08-25 15:37:57, endTime=2025-08-26 15:37:59, page=1, pageSize=30, deviceConfigId=1)
2025-08-26 15:39:59.925 [async-task-pool14] INFO  c.r.f.a.d.FlowRiskAssetsParam - [getRequestBase,49] - 非凡API流量风险资产请求参数: https://***************:23000/v2/flow-risk-assets?access_token=lst2BAzxJTQL0eSZ&risk_type=all&start_date=2025-08-25%2015%3A37%3A57&end_date=2025-08-26%2015%3A37%3A59&page=1&page_size=30
2025-08-26 15:39:59.926 [async-task-pool101] INFO  c.r.f.a.d.FlowRiskAssetsParam - [getRequestBase,49] - 非凡API流量风险资产请求参数: https://***************:23000/v2/flow-risk-assets?access_token=lst2BAzxJTQL0eSZ&risk_type=all&start_date=2025-08-25%2015%3A37%3A57&end_date=2025-08-26%2015%3A37%3A59&page=1&page_size=30
2025-08-26 15:39:59.959 [pool-4-thread-1] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [lambda$0,81] - 最大连接：200 当前连接: 2
2025-08-26 15:40:00.004 [async-task-pool14] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,458] - 流量风险资产总数: 6
2025-08-26 15:40:00.008 [async-task-pool14] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,481] - 第1页获取6条数据
2025-08-26 15:40:00.013 [async-task-pool101] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,458] - 流量风险资产总数: 6
2025-08-26 15:40:00.014 [async-task-pool14] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,488] - 当前页数据量(6)少于页大小(30)，已获取完所有数据
2025-08-26 15:40:00.015 [async-task-pool101] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,481] - 第1页获取6条数据
2025-08-26 15:40:00.019 [async-task-pool101] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,488] - 当前页数据量(6)少于页大小(30)，已获取完所有数据
2025-08-26 15:40:00.129 [async-task-pool14] INFO  c.r.m.c.c.FfsafeClientService - [processBatch,662] - 批量更新6条流量风险资产数据
2025-08-26 15:40:00.136 [async-task-pool14] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,551] - 拉取流量风险资产数据完成，耗时215ms，共1页，共6条数据，处理结果: true
2025-08-26 15:40:00.187 [async-task-pool101] INFO  c.r.m.c.c.FfsafeClientService - [processBatch,662] - 批量更新6条流量风险资产数据
2025-08-26 15:40:00.213 [async-task-pool101] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,551] - 拉取流量风险资产数据完成，耗时291ms，共1页，共6条数据，处理结果: true
2025-08-26 15:40:00.237 [Thread-7] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 327毫秒
2025-08-26 15:40:06.092 [Thread-5] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:40:06.092 [Thread-5] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：1,等待任务: 0
2025-08-26 15:40:06.093 [async-task-pool83] INFO  c.r.f.a.e.PullDataEvent - [lambda$1,102] - 开始流量告警日志同步: 测试非凡设备1
2025-08-26 15:40:06.093 [async-task-pool124] INFO  c.r.f.a.e.PullDataEvent - [lambda$1,102] - 开始流量告警日志同步: 测试设备2
2025-08-26 15:40:06.720 [Thread-5] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 628毫秒
2025-08-26 15:40:09.919 [taskScheduler-9] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:40:09.919 [taskScheduler-9] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：1,等待任务: 0
2025-08-26 15:40:09.935 [Thread-6] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:40:09.935 [Thread-6] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：3,等待任务: 0
2025-08-26 15:40:09.936 [async-task-pool120] INFO  c.r.f.a.e.PullFilterLogEvent - [lambda$1,90] - 开始获取IP阻断日志: 测试非凡设备1
2025-08-26 15:40:09.936 [async-task-pool123] INFO  c.r.f.a.e.PullFilterLogEvent - [lambda$1,90] - 开始获取IP阻断日志: 测试设备2
2025-08-26 15:40:09.942 [Thread-8] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:40:09.943 [Thread-8] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：5,等待任务: 0
2025-08-26 15:40:09.944 [async-task-pool133] INFO  c.r.f.a.e.PullHostIntrusionAttackEvent - [lambda$1,83] - 开始获取主机入侵攻击数据: 测试设备2
2025-08-26 15:40:09.944 [async-task-pool103] INFO  c.r.f.a.e.PullHostIntrusionAttackEvent - [lambda$1,83] - 开始获取主机入侵攻击数据: 测试非凡设备1
2025-08-26 15:40:09.945 [async-task-pool133] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,874] - 开始拉取主机入侵攻击数据，参数：HostIntrusionAttackParam(accessToken=null, sip=null, dip=null, startDate=2025-08-25 15:37:59, endDate=2025-08-26 15:38:09, page=1, pSize=20, deviceConfigId=4)
2025-08-26 15:40:09.945 [async-task-pool103] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,874] - 开始拉取主机入侵攻击数据，参数：HostIntrusionAttackParam(accessToken=null, sip=null, dip=null, startDate=2025-08-25 15:37:59, endDate=2025-08-26 15:38:09, page=1, pSize=20, deviceConfigId=1)
2025-08-26 15:40:09.946 [async-task-pool133] INFO  c.r.f.a.d.HostIntrusionAttackParam - [getRequestBase,80] - 非凡API主机入侵攻击分页请求参数: https://***************:23000/v2/host-edr-intrusion?access_token=lst2BAzxJTQL0eSZ&start_date=2025-08-25%2015%3A37%3A59&end_date=2025-08-26%2015%3A38%3A09&page=1&p_size=20
2025-08-26 15:40:09.953 [async-task-pool103] INFO  c.r.f.a.d.HostIntrusionAttackParam - [getRequestBase,80] - 非凡API主机入侵攻击分页请求参数: https://***************:23000/v2/host-edr-intrusion?access_token=lst2BAzxJTQL0eSZ&start_date=2025-08-25%2015%3A37%3A59&end_date=2025-08-26%2015%3A38%3A09&page=1&p_size=20
2025-08-26 15:40:10.004 [async-task-pool117] INFO  c.r.f.c.FFSafeRequestComponent - [execRequest,88] - 非凡请求响应: [{"id": 1058, "block_ip": "*************", "location": ["美国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-22 00:58:57", "release_time": "2025-08-29 01:00:59", "remarks": ""}, {"id": 1057, "block_ip": "************", "location": ["法国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-21 13:55:52", "release_time": "2025-08-28 13:56:53", "remarks": ""}, {"id": 1056, "block_ip": "***********", "location": ["印度"], "action": "阻断", "conditions": ["外网恶意攻击"], "type": "流量检测", "create_time": "2025-08-20 12:44:55", "release_time": "2025-08-27 12:46:57", "remarks": ""}]

2025-08-26 15:40:10.004 [pool-4-thread-1] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [lambda$0,81] - 最大连接：200 当前连接: 2
2025-08-26 15:40:10.010 [async-task-pool122] INFO  c.r.f.c.FFSafeRequestComponent - [execRequest,88] - 非凡请求响应: [{"id": 1058, "block_ip": "*************", "location": ["美国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-22 00:58:57", "release_time": "2025-08-29 01:00:59", "remarks": ""}, {"id": 1057, "block_ip": "************", "location": ["法国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-21 13:55:52", "release_time": "2025-08-28 13:56:53", "remarks": ""}, {"id": 1056, "block_ip": "***********", "location": ["印度"], "action": "阻断", "conditions": ["外网恶意攻击"], "type": "流量检测", "create_time": "2025-08-20 12:44:55", "release_time": "2025-08-27 12:46:57", "remarks": ""}]

2025-08-26 15:40:10.047 [async-task-pool103] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,987] - 主机入侵攻击总数: 0
2025-08-26 15:40:10.049 [async-task-pool103] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1010] - 第1页获取0条数据
2025-08-26 15:40:10.050 [async-task-pool133] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,987] - 主机入侵攻击总数: 0
2025-08-26 15:40:10.050 [async-task-pool103] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1014] - 当前页无数据，停止获取
2025-08-26 15:40:10.051 [async-task-pool133] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1010] - 第1页获取0条数据
2025-08-26 15:40:10.053 [async-task-pool103] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1083] - 拉取主机入侵攻击数据成功，但没有有效数据
2025-08-26 15:40:10.054 [async-task-pool133] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1014] - 当前页无数据，停止获取
2025-08-26 15:40:10.054 [async-task-pool133] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1083] - 拉取主机入侵攻击数据成功，但没有有效数据
2025-08-26 15:40:10.060 [taskScheduler-9] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 141毫秒
2025-08-26 15:40:10.076 [Thread-8] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 134毫秒
2025-08-26 15:40:10.094 [Thread-6] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 159毫秒
2025-08-26 15:40:10.291 [Thread-7] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:40:10.291 [Thread-7] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：1,等待任务: 0
2025-08-26 15:40:10.292 [async-task-pool118] INFO  c.r.f.a.e.PullFlowRiskAssetsEvent - [lambda$1,100] - 开始获取流量风险资产数据: 测试非凡设备1
2025-08-26 15:40:10.292 [async-task-pool118] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,352] - 开始拉取流量风险资产数据，参数：FlowRiskAssetsParam(accessToken=null, riskType=all, startTime=2025-08-25 15:38:07, endTime=2025-08-26 15:38:10, page=1, pageSize=30, deviceConfigId=1)
2025-08-26 15:40:10.292 [async-task-pool128] INFO  c.r.f.a.e.PullFlowRiskAssetsEvent - [lambda$1,100] - 开始获取流量风险资产数据: 测试设备2
2025-08-26 15:40:10.294 [async-task-pool128] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,352] - 开始拉取流量风险资产数据，参数：FlowRiskAssetsParam(accessToken=null, riskType=all, startTime=2025-08-25 15:38:07, endTime=2025-08-26 15:38:10, page=1, pageSize=30, deviceConfigId=4)
2025-08-26 15:40:10.294 [async-task-pool118] INFO  c.r.f.a.d.FlowRiskAssetsParam - [getRequestBase,49] - 非凡API流量风险资产请求参数: https://***************:23000/v2/flow-risk-assets?access_token=lst2BAzxJTQL0eSZ&risk_type=all&start_date=2025-08-25%2015%3A38%3A07&end_date=2025-08-26%2015%3A38%3A10&page=1&page_size=30
2025-08-26 15:40:10.295 [async-task-pool128] INFO  c.r.f.a.d.FlowRiskAssetsParam - [getRequestBase,49] - 非凡API流量风险资产请求参数: https://***************:23000/v2/flow-risk-assets?access_token=lst2BAzxJTQL0eSZ&risk_type=all&start_date=2025-08-25%2015%3A38%3A07&end_date=2025-08-26%2015%3A38%3A10&page=1&page_size=30
2025-08-26 15:40:10.375 [async-task-pool118] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,458] - 流量风险资产总数: 6
2025-08-26 15:40:10.376 [async-task-pool118] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,481] - 第1页获取6条数据
2025-08-26 15:40:10.376 [async-task-pool118] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,488] - 当前页数据量(6)少于页大小(30)，已获取完所有数据
2025-08-26 15:40:10.384 [async-task-pool128] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,458] - 流量风险资产总数: 6
2025-08-26 15:40:10.385 [async-task-pool128] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,481] - 第1页获取6条数据
2025-08-26 15:40:10.386 [async-task-pool128] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,488] - 当前页数据量(6)少于页大小(30)，已获取完所有数据
2025-08-26 15:40:10.404 [async-task-pool118] INFO  c.r.m.c.c.FfsafeClientService - [processBatch,662] - 批量更新6条流量风险资产数据
2025-08-26 15:40:10.406 [async-task-pool118] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,551] - 拉取流量风险资产数据完成，耗时114ms，共1页，共6条数据，处理结果: true
2025-08-26 15:40:10.413 [async-task-pool128] INFO  c.r.m.c.c.FfsafeClientService - [processBatch,662] - 批量更新6条流量风险资产数据
2025-08-26 15:40:10.413 [async-task-pool128] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,551] - 拉取流量风险资产数据完成，耗时119ms，共1页，共6条数据，处理结果: true
2025-08-26 15:40:10.420 [Thread-7] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 129毫秒
2025-08-26 15:40:12.063 [pool-6-thread-5] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 13秒411毫秒
2025-08-26 15:40:12.336 [pool-6-thread-5] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sync,255] - 批量更新攻击者视角告警列开始: 11
2025-08-26 15:40:12.370 [pool-6-thread-5] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=*************, operationType=UPDATE
2025-08-26 15:40:12.372 [pool-6-thread-5] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=**********0, operationType=UPDATE
2025-08-26 15:40:12.377 [pool-6-thread-5] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=************, operationType=UPDATE
2025-08-26 15:40:12.379 [pool-6-thread-5] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=***********, operationType=UPDATE
2025-08-26 15:40:12.380 [pool-6-thread-5] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=***************, operationType=UPDATE
2025-08-26 15:40:12.382 [pool-6-thread-5] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=***********, operationType=UPDATE
2025-08-26 15:40:12.386 [pool-6-thread-5] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=**************, operationType=UPDATE
2025-08-26 15:40:12.388 [pool-6-thread-5] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=***********, operationType=UPDATE
2025-08-26 15:40:12.394 [pool-6-thread-5] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=***********, operationType=UPDATE
2025-08-26 15:40:12.397 [pool-6-thread-5] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=**********, operationType=UPDATE
2025-08-26 15:40:12.399 [pool-6-thread-5] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=***********, operationType=UPDATE
2025-08-26 15:40:12.462 [pool-6-thread-1] INFO  c.r.s.t.FfSafeFlowAlarmTask - [syncTask,46] - 开始同步攻击者告警数据
2025-08-26 15:40:12.527 [pool-6-thread-1] INFO  c.r.f.c.FFSafeRequestComponent - [execRequest,88] - 非凡请求响应: [{"attack_ip": "**********0", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 1, "attack_type_nums": 1, "attack_nums": 584, "start_time": "2025-08-25 08:59:16", "update_time": "2025-08-26 15:38:31"}, {"attack_ip": "************", "risk_level": 3, "ip_tags": ["内网恶意攻击"], "location": "局域网", "victim_ip_nums": 5, "attack_type_nums": 6, "attack_nums": 97, "start_time": "2025-08-25 16:17:50", "update_time": "2025-08-26 15:37:44"}, {"attack_ip": "***********", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 1, "attack_type_nums": 1, "attack_nums": 239, "start_time": "2025-08-26 09:54:16", "update_time": "2025-08-26 15:37:42"}, {"attack_ip": "***************", "risk_level": 3, "ip_tags": ["内网端口扫描", "内网恶意攻击", "暴力破解"], "location": "局域网", "victim_ip_nums": 30, "attack_type_nums": 1079, "attack_nums": 58440, "start_time": "2025-08-26 14:58:22", "update_time": "2025-08-26 15:34:10"}, {"attack_ip": "***********", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 2, "attack_type_nums": 2, "attack_nums": 155, "start_time": "2025-08-25 13:27:18", "update_time": "2025-08-26 15:33:16"}, {"attack_ip": "**************", "risk_level": 3, "ip_tags": ["内网端口扫描", "内网恶意攻击", "暴力破解"], "location": "局域网", "victim_ip_nums": 257, "attack_type_nums": 62, "attack_nums": 1176, "start_time": "2025-08-26 14:24:32", "update_time": "2025-08-26 15:32:50"}, {"attack_ip": "***********", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 1, "attack_type_nums": 3, "attack_nums": 106, "start_time": "2025-08-26 09:49:05", "update_time": "2025-08-26 15:31:50"}, {"attack_ip": "***********", "risk_level": 2, "ip_tags": ["踩中蜜罐", "内网可疑行为"], "location": "局域网", "victim_ip_nums": 2, "attack_type_nums": 6, "attack_nums": 115, "start_time": "2025-08-25 14:42:32", "update_time": "2025-08-26 15:30:35"}, {"attack_ip": "**********", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 1, "attack_type_nums": 1, "attack_nums": 403, "start_time": "2025-08-25 08:14:50", "update_time": "2025-08-26 15:29:54"}, {"attack_ip": "***********", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 1, "attack_type_nums": 1, "attack_nums": 277, "start_time": "2025-08-25 08:27:55", "update_time": "2025-08-26 15:29:41"}]

2025-08-26 15:40:12.534 [pool-6-thread-1] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:10...
2025-08-26 15:40:12.534 [pool-6-thread-1] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：0,等待任务: 0
2025-08-26 15:40:17.212 [Thread-5] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:40:17.241 [Thread-5] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：1,等待任务: 0
2025-08-26 15:40:17.241 [async-task-pool127] INFO  c.r.f.a.e.PullDataEvent - [lambda$1,102] - 开始流量告警日志同步: 测试非凡设备1
2025-08-26 15:40:17.241 [async-task-pool138] INFO  c.r.f.a.e.PullDataEvent - [lambda$1,102] - 开始流量告警日志同步: 测试设备2
2025-08-26 15:40:17.883 [Thread-5] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 671毫秒
2025-08-26 15:40:20.008 [pool-4-thread-1] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [lambda$0,81] - 最大连接：200 当前连接: 2
2025-08-26 15:40:20.112 [taskScheduler-9] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:40:20.114 [taskScheduler-9] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：1,等待任务: 0
2025-08-26 15:40:20.142 [Thread-8] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:40:20.143 [Thread-6] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:40:20.143 [Thread-8] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：3,等待任务: 0
2025-08-26 15:40:20.143 [Thread-6] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：3,等待任务: 0
2025-08-26 15:40:20.143 [async-task-pool141] INFO  c.r.f.a.e.PullHostIntrusionAttackEvent - [lambda$1,83] - 开始获取主机入侵攻击数据: 测试非凡设备1
2025-08-26 15:40:20.143 [async-task-pool136] INFO  c.r.f.a.e.PullHostIntrusionAttackEvent - [lambda$1,83] - 开始获取主机入侵攻击数据: 测试设备2
2025-08-26 15:40:20.144 [async-task-pool119] INFO  c.r.f.a.e.PullFilterLogEvent - [lambda$1,90] - 开始获取IP阻断日志: 测试非凡设备1
2025-08-26 15:40:20.144 [async-task-pool97] INFO  c.r.f.a.e.PullFilterLogEvent - [lambda$1,90] - 开始获取IP阻断日志: 测试设备2
2025-08-26 15:40:20.144 [async-task-pool141] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,874] - 开始拉取主机入侵攻击数据，参数：HostIntrusionAttackParam(accessToken=null, sip=null, dip=null, startDate=2025-08-25 15:38:09, endDate=2025-08-26 15:38:20, page=1, pSize=20, deviceConfigId=1)
2025-08-26 15:40:20.144 [async-task-pool136] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,874] - 开始拉取主机入侵攻击数据，参数：HostIntrusionAttackParam(accessToken=null, sip=null, dip=null, startDate=2025-08-25 15:38:09, endDate=2025-08-26 15:38:20, page=1, pSize=20, deviceConfigId=4)
2025-08-26 15:40:20.147 [async-task-pool141] INFO  c.r.f.a.d.HostIntrusionAttackParam - [getRequestBase,80] - 非凡API主机入侵攻击分页请求参数: https://***************:23000/v2/host-edr-intrusion?access_token=lst2BAzxJTQL0eSZ&start_date=2025-08-25%2015%3A38%3A09&end_date=2025-08-26%2015%3A38%3A20&page=1&p_size=20
2025-08-26 15:40:20.148 [async-task-pool136] INFO  c.r.f.a.d.HostIntrusionAttackParam - [getRequestBase,80] - 非凡API主机入侵攻击分页请求参数: https://***************:23000/v2/host-edr-intrusion?access_token=lst2BAzxJTQL0eSZ&start_date=2025-08-25%2015%3A38%3A09&end_date=2025-08-26%2015%3A38%3A20&page=1&p_size=20
2025-08-26 15:40:20.320 [async-task-pool110] INFO  c.r.f.c.FFSafeRequestComponent - [execRequest,88] - 非凡请求响应: [{"id": 1058, "block_ip": "*************", "location": ["美国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-22 00:58:57", "release_time": "2025-08-29 01:00:59", "remarks": ""}, {"id": 1057, "block_ip": "************", "location": ["法国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-21 13:55:52", "release_time": "2025-08-28 13:56:53", "remarks": ""}, {"id": 1056, "block_ip": "***********", "location": ["印度"], "action": "阻断", "conditions": ["外网恶意攻击"], "type": "流量检测", "create_time": "2025-08-20 12:44:55", "release_time": "2025-08-27 12:46:57", "remarks": ""}]

2025-08-26 15:40:20.330 [async-task-pool93] INFO  c.r.f.c.FFSafeRequestComponent - [execRequest,88] - 非凡请求响应: [{"id": 1058, "block_ip": "*************", "location": ["美国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-22 00:58:57", "release_time": "2025-08-29 01:00:59", "remarks": ""}, {"id": 1057, "block_ip": "************", "location": ["法国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-21 13:55:52", "release_time": "2025-08-28 13:56:53", "remarks": ""}, {"id": 1056, "block_ip": "***********", "location": ["印度"], "action": "阻断", "conditions": ["外网恶意攻击"], "type": "流量检测", "create_time": "2025-08-20 12:44:55", "release_time": "2025-08-27 12:46:57", "remarks": ""}]

2025-08-26 15:40:20.335 [async-task-pool136] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,987] - 主机入侵攻击总数: 0
2025-08-26 15:40:20.335 [async-task-pool136] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1010] - 第1页获取0条数据
2025-08-26 15:40:20.336 [async-task-pool136] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1014] - 当前页无数据，停止获取
2025-08-26 15:40:20.337 [async-task-pool141] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,987] - 主机入侵攻击总数: 0
2025-08-26 15:40:20.337 [async-task-pool136] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1083] - 拉取主机入侵攻击数据成功，但没有有效数据
2025-08-26 15:40:20.341 [async-task-pool141] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1010] - 第1页获取0条数据
2025-08-26 15:40:20.345 [async-task-pool141] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1014] - 当前页无数据，停止获取
2025-08-26 15:40:20.346 [async-task-pool141] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1083] - 拉取主机入侵攻击数据成功，但没有有效数据
2025-08-26 15:40:20.362 [Thread-8] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 220毫秒
2025-08-26 15:40:20.378 [Thread-6] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 235毫秒
2025-08-26 15:40:20.379 [taskScheduler-9] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 267毫秒
2025-08-26 15:40:20.431 [Thread-7] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:40:20.432 [Thread-7] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：1,等待任务: 0
2025-08-26 15:40:20.432 [async-task-pool154] INFO  c.r.f.a.e.PullFlowRiskAssetsEvent - [lambda$1,100] - 开始获取流量风险资产数据: 测试非凡设备1
2025-08-26 15:40:20.432 [async-task-pool113] INFO  c.r.f.a.e.PullFlowRiskAssetsEvent - [lambda$1,100] - 开始获取流量风险资产数据: 测试设备2
2025-08-26 15:40:20.433 [async-task-pool154] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,352] - 开始拉取流量风险资产数据，参数：FlowRiskAssetsParam(accessToken=null, riskType=all, startTime=2025-08-25 15:38:17, endTime=2025-08-26 15:38:20, page=1, pageSize=30, deviceConfigId=1)
2025-08-26 15:40:20.433 [async-task-pool113] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,352] - 开始拉取流量风险资产数据，参数：FlowRiskAssetsParam(accessToken=null, riskType=all, startTime=2025-08-25 15:38:17, endTime=2025-08-26 15:38:20, page=1, pageSize=30, deviceConfigId=4)
2025-08-26 15:40:20.434 [async-task-pool154] INFO  c.r.f.a.d.FlowRiskAssetsParam - [getRequestBase,49] - 非凡API流量风险资产请求参数: https://***************:23000/v2/flow-risk-assets?access_token=lst2BAzxJTQL0eSZ&risk_type=all&start_date=2025-08-25%2015%3A38%3A17&end_date=2025-08-26%2015%3A38%3A20&page=1&page_size=30
2025-08-26 15:40:20.434 [async-task-pool113] INFO  c.r.f.a.d.FlowRiskAssetsParam - [getRequestBase,49] - 非凡API流量风险资产请求参数: https://***************:23000/v2/flow-risk-assets?access_token=lst2BAzxJTQL0eSZ&risk_type=all&start_date=2025-08-25%2015%3A38%3A17&end_date=2025-08-26%2015%3A38%3A20&page=1&page_size=30
2025-08-26 15:40:20.524 [async-task-pool113] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,458] - 流量风险资产总数: 6
2025-08-26 15:40:20.524 [async-task-pool113] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,481] - 第1页获取6条数据
2025-08-26 15:40:20.525 [async-task-pool113] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,488] - 当前页数据量(6)少于页大小(30)，已获取完所有数据
2025-08-26 15:40:20.532 [async-task-pool154] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,458] - 流量风险资产总数: 6
2025-08-26 15:40:20.533 [async-task-pool154] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,481] - 第1页获取6条数据
2025-08-26 15:40:20.534 [async-task-pool154] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,488] - 当前页数据量(6)少于页大小(30)，已获取完所有数据
2025-08-26 15:40:20.547 [async-task-pool113] INFO  c.r.m.c.c.FfsafeClientService - [processBatch,662] - 批量更新6条流量风险资产数据
2025-08-26 15:40:20.548 [async-task-pool113] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,551] - 拉取流量风险资产数据完成，耗时115ms，共1页，共6条数据，处理结果: true
2025-08-26 15:40:20.565 [async-task-pool154] INFO  c.r.m.c.c.FfsafeClientService - [processBatch,662] - 批量更新6条流量风险资产数据
2025-08-26 15:40:20.565 [async-task-pool154] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,551] - 拉取流量风险资产数据完成，耗时132ms，共1页，共6条数据，处理结果: true
2025-08-26 15:40:20.569 [Thread-7] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 138毫秒
2025-08-26 15:40:28.126 [Thread-5] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:40:28.143 [Thread-5] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：1,等待任务: 0
2025-08-26 15:40:28.189 [async-task-pool126] INFO  c.r.f.a.e.PullDataEvent - [lambda$1,102] - 开始流量告警日志同步: 测试非凡设备1
2025-08-26 15:40:28.189 [async-task-pool112] INFO  c.r.f.a.e.PullDataEvent - [lambda$1,102] - 开始流量告警日志同步: 测试设备2
2025-08-26 15:40:28.551 [Thread-5] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 425毫秒
2025-08-26 15:40:30.018 [pool-4-thread-1] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [lambda$0,81] - 最大连接：200 当前连接: 2
2025-08-26 15:40:30.396 [pool-6-thread-1] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 17秒862毫秒
2025-08-26 15:40:30.458 [Thread-8] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:40:30.458 [taskScheduler-9] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:40:30.458 [Thread-6] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:40:30.459 [Thread-8] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：0,等待任务: 0
2025-08-26 15:40:30.459 [taskScheduler-9] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：0,等待任务: 0
2025-08-26 15:40:30.459 [Thread-6] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：0,等待任务: 0
2025-08-26 15:40:30.460 [async-task-pool143] INFO  c.r.f.a.e.PullHostIntrusionAttackEvent - [lambda$1,83] - 开始获取主机入侵攻击数据: 测试设备2
2025-08-26 15:40:30.460 [async-task-pool161] INFO  c.r.f.a.e.PullHostIntrusionAttackEvent - [lambda$1,83] - 开始获取主机入侵攻击数据: 测试非凡设备1
2025-08-26 15:40:30.461 [async-task-pool135] INFO  c.r.f.a.e.PullFilterLogEvent - [lambda$1,90] - 开始获取IP阻断日志: 测试设备2
2025-08-26 15:40:30.461 [async-task-pool78] INFO  c.r.f.a.e.PullFilterLogEvent - [lambda$1,90] - 开始获取IP阻断日志: 测试非凡设备1
2025-08-26 15:40:30.475 [async-task-pool143] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,874] - 开始拉取主机入侵攻击数据，参数：HostIntrusionAttackParam(accessToken=null, sip=null, dip=null, startDate=2025-08-25 15:38:20, endDate=2025-08-26 15:38:30, page=1, pSize=20, deviceConfigId=4)
2025-08-26 15:40:30.475 [async-task-pool161] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,874] - 开始拉取主机入侵攻击数据，参数：HostIntrusionAttackParam(accessToken=null, sip=null, dip=null, startDate=2025-08-25 15:38:20, endDate=2025-08-26 15:38:30, page=1, pSize=20, deviceConfigId=1)
2025-08-26 15:40:30.481 [async-task-pool143] INFO  c.r.f.a.d.HostIntrusionAttackParam - [getRequestBase,80] - 非凡API主机入侵攻击分页请求参数: https://***************:23000/v2/host-edr-intrusion?access_token=lst2BAzxJTQL0eSZ&start_date=2025-08-25%2015%3A38%3A20&end_date=2025-08-26%2015%3A38%3A30&page=1&p_size=20
2025-08-26 15:40:30.488 [async-task-pool161] INFO  c.r.f.a.d.HostIntrusionAttackParam - [getRequestBase,80] - 非凡API主机入侵攻击分页请求参数: https://***************:23000/v2/host-edr-intrusion?access_token=lst2BAzxJTQL0eSZ&start_date=2025-08-25%2015%3A38%3A20&end_date=2025-08-26%2015%3A38%3A30&page=1&p_size=20
2025-08-26 15:40:30.560 [async-task-pool162] INFO  c.r.f.c.FFSafeRequestComponent - [execRequest,88] - 非凡请求响应: [{"id": 1058, "block_ip": "*************", "location": ["美国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-22 00:58:57", "release_time": "2025-08-29 01:00:59", "remarks": ""}, {"id": 1057, "block_ip": "************", "location": ["法国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-21 13:55:52", "release_time": "2025-08-28 13:56:53", "remarks": ""}, {"id": 1056, "block_ip": "***********", "location": ["印度"], "action": "阻断", "conditions": ["外网恶意攻击"], "type": "流量检测", "create_time": "2025-08-20 12:44:55", "release_time": "2025-08-27 12:46:57", "remarks": ""}]

2025-08-26 15:40:30.563 [async-task-pool98] INFO  c.r.f.c.FFSafeRequestComponent - [execRequest,88] - 非凡请求响应: [{"id": 1058, "block_ip": "*************", "location": ["美国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-22 00:58:57", "release_time": "2025-08-29 01:00:59", "remarks": ""}, {"id": 1057, "block_ip": "************", "location": ["法国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-21 13:55:52", "release_time": "2025-08-28 13:56:53", "remarks": ""}, {"id": 1056, "block_ip": "***********", "location": ["印度"], "action": "阻断", "conditions": ["外网恶意攻击"], "type": "流量检测", "create_time": "2025-08-20 12:44:55", "release_time": "2025-08-27 12:46:57", "remarks": ""}]

2025-08-26 15:40:30.573 [async-task-pool143] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,987] - 主机入侵攻击总数: 0
2025-08-26 15:40:30.573 [async-task-pool143] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1010] - 第1页获取0条数据
2025-08-26 15:40:30.574 [async-task-pool143] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1014] - 当前页无数据，停止获取
2025-08-26 15:40:30.574 [async-task-pool143] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1083] - 拉取主机入侵攻击数据成功，但没有有效数据
2025-08-26 15:40:30.591 [Thread-7] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:40:30.591 [Thread-7] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：6,等待任务: 0
2025-08-26 15:40:30.592 [async-task-pool163] INFO  c.r.f.a.e.PullFlowRiskAssetsEvent - [lambda$1,100] - 开始获取流量风险资产数据: 测试非凡设备1
2025-08-26 15:40:30.592 [async-task-pool145] INFO  c.r.f.a.e.PullFlowRiskAssetsEvent - [lambda$1,100] - 开始获取流量风险资产数据: 测试设备2
2025-08-26 15:40:30.592 [async-task-pool163] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,352] - 开始拉取流量风险资产数据，参数：FlowRiskAssetsParam(accessToken=null, riskType=all, startTime=2025-08-25 15:38:28, endTime=2025-08-26 15:38:30, page=1, pageSize=30, deviceConfigId=1)
2025-08-26 15:40:30.593 [async-task-pool145] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,352] - 开始拉取流量风险资产数据，参数：FlowRiskAssetsParam(accessToken=null, riskType=all, startTime=2025-08-25 15:38:28, endTime=2025-08-26 15:38:30, page=1, pageSize=30, deviceConfigId=4)
2025-08-26 15:40:30.593 [taskScheduler-9] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 135毫秒
2025-08-26 15:40:30.594 [async-task-pool163] INFO  c.r.f.a.d.FlowRiskAssetsParam - [getRequestBase,49] - 非凡API流量风险资产请求参数: https://***************:23000/v2/flow-risk-assets?access_token=lst2BAzxJTQL0eSZ&risk_type=all&start_date=2025-08-25%2015%3A38%3A28&end_date=2025-08-26%2015%3A38%3A30&page=1&page_size=30
2025-08-26 15:40:30.595 [async-task-pool145] INFO  c.r.f.a.d.FlowRiskAssetsParam - [getRequestBase,49] - 非凡API流量风险资产请求参数: https://***************:23000/v2/flow-risk-assets?access_token=lst2BAzxJTQL0eSZ&risk_type=all&start_date=2025-08-25%2015%3A38%3A28&end_date=2025-08-26%2015%3A38%3A30&page=1&page_size=30
2025-08-26 15:40:30.604 [async-task-pool161] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,987] - 主机入侵攻击总数: 0
2025-08-26 15:40:30.606 [async-task-pool161] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1010] - 第1页获取0条数据
2025-08-26 15:40:30.607 [async-task-pool161] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1014] - 当前页无数据，停止获取
2025-08-26 15:40:30.608 [async-task-pool161] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1083] - 拉取主机入侵攻击数据成功，但没有有效数据
2025-08-26 15:40:30.640 [Thread-8] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 182毫秒
2025-08-26 15:40:30.644 [Thread-6] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 186毫秒
2025-08-26 15:40:30.670 [async-task-pool163] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,458] - 流量风险资产总数: 6
2025-08-26 15:40:30.671 [async-task-pool163] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,481] - 第1页获取6条数据
2025-08-26 15:40:30.671 [async-task-pool163] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,488] - 当前页数据量(6)少于页大小(30)，已获取完所有数据
2025-08-26 15:40:30.688 [async-task-pool163] INFO  c.r.m.c.c.FfsafeClientService - [processBatch,662] - 批量更新6条流量风险资产数据
2025-08-26 15:40:30.690 [async-task-pool163] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,551] - 拉取流量风险资产数据完成，耗时98ms，共1页，共6条数据，处理结果: true
2025-08-26 15:40:30.692 [async-task-pool145] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,458] - 流量风险资产总数: 6
2025-08-26 15:40:30.692 [async-task-pool145] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,481] - 第1页获取6条数据
2025-08-26 15:40:30.693 [async-task-pool145] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,488] - 当前页数据量(6)少于页大小(30)，已获取完所有数据
2025-08-26 15:40:30.714 [async-task-pool145] INFO  c.r.m.c.c.FfsafeClientService - [processBatch,662] - 批量更新6条流量风险资产数据
2025-08-26 15:40:30.716 [async-task-pool145] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,551] - 拉取流量风险资产数据完成，耗时123ms，共1页，共6条数据，处理结果: true
2025-08-26 15:40:30.737 [Thread-7] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 146毫秒
2025-08-26 15:40:30.839 [pool-6-thread-1] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sync,255] - 批量更新攻击者视角告警列开始: 10
2025-08-26 15:40:30.859 [pool-6-thread-1] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=**********0, operationType=UPDATE
2025-08-26 15:40:30.860 [pool-6-thread-1] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=************, operationType=UPDATE
2025-08-26 15:40:30.862 [pool-6-thread-1] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=***********, operationType=UPDATE
2025-08-26 15:40:30.863 [pool-6-thread-1] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=***************, operationType=UPDATE
2025-08-26 15:40:30.867 [pool-6-thread-1] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=***********, operationType=UPDATE
2025-08-26 15:40:30.868 [pool-6-thread-1] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=**************, operationType=UPDATE
2025-08-26 15:40:30.869 [pool-6-thread-1] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=***********, operationType=UPDATE
2025-08-26 15:40:30.871 [pool-6-thread-1] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=***********, operationType=UPDATE
2025-08-26 15:40:30.873 [pool-6-thread-1] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=**********, operationType=UPDATE
2025-08-26 15:40:30.875 [pool-6-thread-1] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=***********, operationType=UPDATE
2025-08-26 15:40:30.877 [pool-6-thread-4] INFO  c.r.s.t.FfSafeFlowAlarmTask - [syncTask,46] - 开始同步攻击者告警数据
2025-08-26 15:40:30.919 [pool-6-thread-4] INFO  c.r.f.c.FFSafeRequestComponent - [execRequest,88] - 非凡请求响应: [{"attack_ip": "************", "risk_level": 3, "ip_tags": ["内网恶意攻击"], "location": "局域网", "victim_ip_nums": 5, "attack_type_nums": 6, "attack_nums": 97, "start_time": "2025-08-25 16:17:50", "update_time": "2025-08-26 15:37:44"}, {"attack_ip": "***********", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 1, "attack_type_nums": 1, "attack_nums": 239, "start_time": "2025-08-26 09:54:16", "update_time": "2025-08-26 15:37:42"}, {"attack_ip": "***************", "risk_level": 3, "ip_tags": ["内网端口扫描", "内网恶意攻击", "暴力破解"], "location": "局域网", "victim_ip_nums": 30, "attack_type_nums": 1079, "attack_nums": 58440, "start_time": "2025-08-26 14:58:22", "update_time": "2025-08-26 15:34:10"}, {"attack_ip": "***********", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 2, "attack_type_nums": 2, "attack_nums": 155, "start_time": "2025-08-25 13:27:18", "update_time": "2025-08-26 15:33:16"}, {"attack_ip": "**************", "risk_level": 3, "ip_tags": ["内网端口扫描", "内网恶意攻击", "暴力破解"], "location": "局域网", "victim_ip_nums": 257, "attack_type_nums": 62, "attack_nums": 1176, "start_time": "2025-08-26 14:24:32", "update_time": "2025-08-26 15:32:50"}, {"attack_ip": "***********", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 1, "attack_type_nums": 3, "attack_nums": 106, "start_time": "2025-08-26 09:49:05", "update_time": "2025-08-26 15:31:50"}, {"attack_ip": "***********", "risk_level": 2, "ip_tags": ["踩中蜜罐", "内网可疑行为"], "location": "局域网", "victim_ip_nums": 2, "attack_type_nums": 6, "attack_nums": 115, "start_time": "2025-08-25 14:42:32", "update_time": "2025-08-26 15:30:35"}, {"attack_ip": "**********", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 1, "attack_type_nums": 1, "attack_nums": 403, "start_time": "2025-08-25 08:14:50", "update_time": "2025-08-26 15:29:54"}, {"attack_ip": "***********", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 1, "attack_type_nums": 1, "attack_nums": 277, "start_time": "2025-08-25 08:27:55", "update_time": "2025-08-26 15:29:41"}]

2025-08-26 15:40:30.923 [pool-6-thread-4] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:9...
2025-08-26 15:40:30.924 [pool-6-thread-4] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：0,等待任务: 0
2025-08-26 15:40:34.846 [taskScheduler-49] INFO  c.r.r.s.HandleDataSyncSender - [sendDataSync,230] - 数据同步发送成功, messageId: 67e07d4d-710b-4d79-86d3-57c8ca9b0dca, 加密状态: 已加密, 消息类型: PING
2025-08-26 15:40:34.858 [taskScheduler-49] INFO  c.r.r.t.RabbitMQTask - [ping,44] - 发送心跳消息
2025-08-26 15:40:34.883 [rabbitConnectionFactory10] INFO  c.r.f.c.r.RabbitMQConfig - [lambda$0,290] - 消息已成功发送到交换机, messageId: 67e07d4d-710b-4d79-86d3-57c8ca9b0dca
2025-08-26 15:40:35.022 [taskScheduler-37] INFO  c.r.c.task.HlsTask - [checkHls,65] - hls检查开始...
2025-08-26 15:40:35.083 [taskScheduler-37] INFO  c.r.c.task.HlsTask - [checkHls,86] - hls检查结束...
2025-08-26 15:40:38.689 [Thread-5] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:40:38.690 [Thread-5] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：1,等待任务: 0
2025-08-26 15:40:38.690 [async-task-pool168] INFO  c.r.f.a.e.PullDataEvent - [lambda$1,102] - 开始流量告警日志同步: 测试非凡设备1
2025-08-26 15:40:38.690 [async-task-pool164] INFO  c.r.f.a.e.PullDataEvent - [lambda$1,102] - 开始流量告警日志同步: 测试设备2
2025-08-26 15:40:38.968 [Thread-5] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 279毫秒
2025-08-26 15:40:40.065 [pool-4-thread-1] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [lambda$0,81] - 最大连接：200 当前连接: 2
2025-08-26 15:40:40.692 [taskScheduler-39] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:40:40.694 [taskScheduler-39] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：1,等待任务: 0
2025-08-26 15:40:40.726 [Thread-8] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:40:40.728 [Thread-6] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:40:40.728 [Thread-8] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：3,等待任务: 0
2025-08-26 15:40:40.728 [Thread-6] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：3,等待任务: 0
2025-08-26 15:40:40.729 [async-task-pool157] INFO  c.r.f.a.e.PullHostIntrusionAttackEvent - [lambda$1,83] - 开始获取主机入侵攻击数据: 测试非凡设备1
2025-08-26 15:40:40.729 [async-task-pool177] INFO  c.r.f.a.e.PullHostIntrusionAttackEvent - [lambda$1,83] - 开始获取主机入侵攻击数据: 测试设备2
2025-08-26 15:40:40.732 [async-task-pool180] INFO  c.r.f.a.e.PullFilterLogEvent - [lambda$1,90] - 开始获取IP阻断日志: 测试非凡设备1
2025-08-26 15:40:40.732 [async-task-pool158] INFO  c.r.f.a.e.PullFilterLogEvent - [lambda$1,90] - 开始获取IP阻断日志: 测试设备2
2025-08-26 15:40:40.733 [async-task-pool157] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,874] - 开始拉取主机入侵攻击数据，参数：HostIntrusionAttackParam(accessToken=null, sip=null, dip=null, startDate=2025-08-25 15:38:30, endDate=2025-08-26 15:38:40, page=1, pSize=20, deviceConfigId=1)
2025-08-26 15:40:40.733 [async-task-pool177] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,874] - 开始拉取主机入侵攻击数据，参数：HostIntrusionAttackParam(accessToken=null, sip=null, dip=null, startDate=2025-08-25 15:38:30, endDate=2025-08-26 15:38:40, page=1, pSize=20, deviceConfigId=4)
2025-08-26 15:40:40.747 [async-task-pool157] INFO  c.r.f.a.d.HostIntrusionAttackParam - [getRequestBase,80] - 非凡API主机入侵攻击分页请求参数: https://***************:23000/v2/host-edr-intrusion?access_token=lst2BAzxJTQL0eSZ&start_date=2025-08-25%2015%3A38%3A30&end_date=2025-08-26%2015%3A38%3A40&page=1&p_size=20
2025-08-26 15:40:40.749 [async-task-pool177] INFO  c.r.f.a.d.HostIntrusionAttackParam - [getRequestBase,80] - 非凡API主机入侵攻击分页请求参数: https://***************:23000/v2/host-edr-intrusion?access_token=lst2BAzxJTQL0eSZ&start_date=2025-08-25%2015%3A38%3A30&end_date=2025-08-26%2015%3A38%3A40&page=1&p_size=20
2025-08-26 15:40:40.810 [Thread-7] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:40:40.816 [Thread-7] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：7,等待任务: 0
2025-08-26 15:40:40.817 [async-task-pool166] INFO  c.r.f.a.e.PullFlowRiskAssetsEvent - [lambda$1,100] - 开始获取流量风险资产数据: 测试非凡设备1
2025-08-26 15:40:40.817 [async-task-pool144] INFO  c.r.f.a.e.PullFlowRiskAssetsEvent - [lambda$1,100] - 开始获取流量风险资产数据: 测试设备2
2025-08-26 15:40:40.817 [async-task-pool166] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,352] - 开始拉取流量风险资产数据，参数：FlowRiskAssetsParam(accessToken=null, riskType=all, startTime=2025-08-25 15:38:38, endTime=2025-08-26 15:38:40, page=1, pageSize=30, deviceConfigId=1)
2025-08-26 15:40:40.817 [async-task-pool144] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,352] - 开始拉取流量风险资产数据，参数：FlowRiskAssetsParam(accessToken=null, riskType=all, startTime=2025-08-25 15:38:38, endTime=2025-08-26 15:38:40, page=1, pageSize=30, deviceConfigId=4)
2025-08-26 15:40:40.819 [async-task-pool166] INFO  c.r.f.a.d.FlowRiskAssetsParam - [getRequestBase,49] - 非凡API流量风险资产请求参数: https://***************:23000/v2/flow-risk-assets?access_token=lst2BAzxJTQL0eSZ&risk_type=all&start_date=2025-08-25%2015%3A38%3A38&end_date=2025-08-26%2015%3A38%3A40&page=1&page_size=30
2025-08-26 15:40:40.819 [async-task-pool144] INFO  c.r.f.a.d.FlowRiskAssetsParam - [getRequestBase,49] - 非凡API流量风险资产请求参数: https://***************:23000/v2/flow-risk-assets?access_token=lst2BAzxJTQL0eSZ&risk_type=all&start_date=2025-08-25%2015%3A38%3A38&end_date=2025-08-26%2015%3A38%3A40&page=1&page_size=30
2025-08-26 15:40:40.937 [async-task-pool157] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,987] - 主机入侵攻击总数: 0
2025-08-26 15:40:40.939 [async-task-pool157] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1010] - 第1页获取0条数据
2025-08-26 15:40:40.939 [async-task-pool157] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1014] - 当前页无数据，停止获取
2025-08-26 15:40:40.994 [async-task-pool157] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1083] - 拉取主机入侵攻击数据成功，但没有有效数据
2025-08-26 15:40:40.998 [async-task-pool144] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,458] - 流量风险资产总数: 6
2025-08-26 15:40:40.998 [async-task-pool166] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,458] - 流量风险资产总数: 6
2025-08-26 15:40:41.013 [async-task-pool144] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,481] - 第1页获取6条数据
2025-08-26 15:40:41.013 [async-task-pool166] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,481] - 第1页获取6条数据
2025-08-26 15:40:41.014 [async-task-pool144] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,488] - 当前页数据量(6)少于页大小(30)，已获取完所有数据
2025-08-26 15:40:41.014 [async-task-pool166] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,488] - 当前页数据量(6)少于页大小(30)，已获取完所有数据
2025-08-26 15:40:41.015 [async-task-pool170] INFO  c.r.f.c.FFSafeRequestComponent - [execRequest,88] - 非凡请求响应: [{"id": 1058, "block_ip": "*************", "location": ["美国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-22 00:58:57", "release_time": "2025-08-29 01:00:59", "remarks": ""}, {"id": 1057, "block_ip": "************", "location": ["法国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-21 13:55:52", "release_time": "2025-08-28 13:56:53", "remarks": ""}, {"id": 1056, "block_ip": "***********", "location": ["印度"], "action": "阻断", "conditions": ["外网恶意攻击"], "type": "流量检测", "create_time": "2025-08-20 12:44:55", "release_time": "2025-08-27 12:46:57", "remarks": ""}]

2025-08-26 15:40:41.016 [async-task-pool129] INFO  c.r.f.c.FFSafeRequestComponent - [execRequest,88] - 非凡请求响应: [{"id": 1058, "block_ip": "*************", "location": ["美国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-22 00:58:57", "release_time": "2025-08-29 01:00:59", "remarks": ""}, {"id": 1057, "block_ip": "************", "location": ["法国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-21 13:55:52", "release_time": "2025-08-28 13:56:53", "remarks": ""}, {"id": 1056, "block_ip": "***********", "location": ["印度"], "action": "阻断", "conditions": ["外网恶意攻击"], "type": "流量检测", "create_time": "2025-08-20 12:44:55", "release_time": "2025-08-27 12:46:57", "remarks": ""}]

2025-08-26 15:40:41.028 [async-task-pool177] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,987] - 主机入侵攻击总数: 0
2025-08-26 15:40:41.048 [async-task-pool177] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1010] - 第1页获取0条数据
2025-08-26 15:40:41.051 [async-task-pool177] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1014] - 当前页无数据，停止获取
2025-08-26 15:40:41.054 [async-task-pool177] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1083] - 拉取主机入侵攻击数据成功，但没有有效数据
2025-08-26 15:40:41.102 [async-task-pool166] INFO  c.r.m.c.c.FfsafeClientService - [processBatch,662] - 批量更新6条流量风险资产数据
2025-08-26 15:40:41.102 [taskScheduler-39] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 410毫秒
2025-08-26 15:40:41.102 [async-task-pool166] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,551] - 拉取流量风险资产数据完成，耗时285ms，共1页，共6条数据，处理结果: true
2025-08-26 15:40:41.104 [Thread-8] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 378毫秒
2025-08-26 15:40:41.105 [async-task-pool144] INFO  c.r.m.c.c.FfsafeClientService - [processBatch,662] - 批量更新6条流量风险资产数据
2025-08-26 15:40:41.105 [Thread-6] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 377毫秒
2025-08-26 15:40:41.105 [async-task-pool144] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,551] - 拉取流量风险资产数据完成，耗时288ms，共1页，共6条数据，处理结果: true
2025-08-26 15:40:41.131 [Thread-7] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 321毫秒
2025-08-26 15:40:46.922 [pool-6-thread-4] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 15秒999毫秒
2025-08-26 15:40:47.207 [pool-6-thread-4] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sync,255] - 批量更新攻击者视角告警列开始: 9
2025-08-26 15:40:47.237 [pool-6-thread-4] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=************, operationType=UPDATE
2025-08-26 15:40:47.239 [pool-6-thread-4] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=***********, operationType=UPDATE
2025-08-26 15:40:47.241 [pool-6-thread-4] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=***************, operationType=UPDATE
2025-08-26 15:40:47.242 [pool-6-thread-4] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=***********, operationType=UPDATE
2025-08-26 15:40:47.243 [pool-6-thread-4] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=**************, operationType=UPDATE
2025-08-26 15:40:47.244 [pool-6-thread-4] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=***********, operationType=UPDATE
2025-08-26 15:40:47.248 [pool-6-thread-4] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=***********, operationType=UPDATE
2025-08-26 15:40:47.251 [pool-6-thread-4] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=**********, operationType=UPDATE
2025-08-26 15:40:47.253 [pool-6-thread-4] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=***********, operationType=UPDATE
2025-08-26 15:40:47.263 [pool-6-thread-4] INFO  c.r.s.t.FfSafeFlowAlarmTask - [syncTask,46] - 开始同步攻击者告警数据
2025-08-26 15:40:47.323 [pool-6-thread-4] INFO  c.r.f.c.FFSafeRequestComponent - [execRequest,88] - 非凡请求响应: [{"attack_ip": "**************", "risk_level": 3, "ip_tags": ["非法外联"], "location": "局域网", "victim_ip_nums": 2, "attack_type_nums": 2, "attack_nums": 176033, "start_time": "2025-08-25 08:00:04", "update_time": "2025-08-26 15:40:11"}, {"attack_ip": "**************", "risk_level": 2, "ip_tags": ["弱口令登录成功"], "location": "局域网", "victim_ip_nums": 2, "attack_type_nums": 5, "attack_nums": 7377, "start_time": "2025-08-25 08:00:02", "update_time": "2025-08-26 15:40:10"}, {"attack_ip": "**************", "risk_level": 3, "ip_tags": ["暴力破解"], "location": "局域网", "victim_ip_nums": 9, "attack_type_nums": 9, "attack_nums": 14921, "start_time": "2025-08-25 08:00:02", "update_time": "2025-08-26 15:40:05"}, {"attack_ip": "**********0", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 1, "attack_type_nums": 1, "attack_nums": 585, "start_time": "2025-08-25 08:59:16", "update_time": "2025-08-26 15:40:01"}, {"attack_ip": "*************", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 2, "attack_type_nums": 1, "attack_nums": 333, "start_time": "2025-08-25 08:03:53", "update_time": "2025-08-26 15:39:58"}, {"attack_ip": "**************", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 1, "attack_type_nums": 1, "attack_nums": 14, "start_time": "2025-08-26 11:06:34", "update_time": "2025-08-26 15:39:35"}, {"attack_ip": "*************", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 2, "attack_type_nums": 1, "attack_nums": 336, "start_time": "2025-08-25 08:02:29", "update_time": "2025-08-26 15:38:37"}, {"attack_ip": "************", "risk_level": 3, "ip_tags": ["内网恶意攻击"], "location": "局域网", "victim_ip_nums": 5, "attack_type_nums": 6, "attack_nums": 97, "start_time": "2025-08-25 16:17:50", "update_time": "2025-08-26 15:37:44"}, {"attack_ip": "***********", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 1, "attack_type_nums": 1, "attack_nums": 239, "start_time": "2025-08-26 09:54:16", "update_time": "2025-08-26 15:37:42"}, {"attack_ip": "***************", "risk_level": 3, "ip_tags": ["内网端口扫描", "内网恶意攻击", "暴力破解"], "location": "局域网", "victim_ip_nums": 30, "attack_type_nums": 1079, "attack_nums": 58440, "start_time": "2025-08-26 14:58:22", "update_time": "2025-08-26 15:34:10"}, {"attack_ip": "***********", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 2, "attack_type_nums": 2, "attack_nums": 155, "start_time": "2025-08-25 13:27:18", "update_time": "2025-08-26 15:33:16"}, {"attack_ip": "**************", "risk_level": 3, "ip_tags": ["内网端口扫描", "内网恶意攻击", "暴力破解"], "location": "局域网", "victim_ip_nums": 257, "attack_type_nums": 62, "attack_nums": 1176, "start_time": "2025-08-26 14:24:32", "update_time": "2025-08-26 15:32:50"}, {"attack_ip": "***********", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 1, "attack_type_nums": 3, "attack_nums": 106, "start_time": "2025-08-26 09:49:05", "update_time": "2025-08-26 15:31:50"}]

2025-08-26 15:40:47.339 [pool-6-thread-4] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:13...
2025-08-26 15:40:47.340 [pool-6-thread-4] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：0,等待任务: 0
2025-08-26 15:40:48.974 [Thread-5] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:40:48.974 [Thread-5] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：4,等待任务: 0
2025-08-26 15:40:48.974 [async-task-pool140] INFO  c.r.f.a.e.PullDataEvent - [lambda$1,102] - 开始流量告警日志同步: 测试设备2
2025-08-26 15:40:48.974 [async-task-pool176] INFO  c.r.f.a.e.PullDataEvent - [lambda$1,102] - 开始流量告警日志同步: 测试非凡设备1
2025-08-26 15:40:49.136 [Thread-5] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 162毫秒
2025-08-26 15:40:50.230 [pool-4-thread-1] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [lambda$0,81] - 最大连接：200 当前连接: 5
2025-08-26 15:40:51.107 [Thread-8] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:40:51.108 [taskScheduler-39] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:40:51.108 [Thread-8] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：3,等待任务: 0
2025-08-26 15:40:51.108 [taskScheduler-39] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：3,等待任务: 0
2025-08-26 15:40:51.108 [async-task-pool189] INFO  c.r.f.a.e.PullHostIntrusionAttackEvent - [lambda$1,83] - 开始获取主机入侵攻击数据: 测试非凡设备1
2025-08-26 15:40:51.108 [async-task-pool190] INFO  c.r.f.a.e.PullHostIntrusionAttackEvent - [lambda$1,83] - 开始获取主机入侵攻击数据: 测试设备2
2025-08-26 15:40:51.116 [async-task-pool190] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,874] - 开始拉取主机入侵攻击数据，参数：HostIntrusionAttackParam(accessToken=null, sip=null, dip=null, startDate=2025-08-25 15:38:40, endDate=2025-08-26 15:38:51, page=1, pSize=20, deviceConfigId=4)
2025-08-26 15:40:51.113 [async-task-pool189] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,874] - 开始拉取主机入侵攻击数据，参数：HostIntrusionAttackParam(accessToken=null, sip=null, dip=null, startDate=2025-08-25 15:38:40, endDate=2025-08-26 15:38:51, page=1, pSize=20, deviceConfigId=1)
2025-08-26 15:40:51.121 [async-task-pool190] INFO  c.r.f.a.d.HostIntrusionAttackParam - [getRequestBase,80] - 非凡API主机入侵攻击分页请求参数: https://***************:23000/v2/host-edr-intrusion?access_token=lst2BAzxJTQL0eSZ&start_date=2025-08-25%2015%3A38%3A40&end_date=2025-08-26%2015%3A38%3A51&page=1&p_size=20
2025-08-26 15:40:51.124 [async-task-pool189] INFO  c.r.f.a.d.HostIntrusionAttackParam - [getRequestBase,80] - 非凡API主机入侵攻击分页请求参数: https://***************:23000/v2/host-edr-intrusion?access_token=lst2BAzxJTQL0eSZ&start_date=2025-08-25%2015%3A38%3A40&end_date=2025-08-26%2015%3A38%3A51&page=1&p_size=20
2025-08-26 15:40:51.127 [Thread-6] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:40:51.127 [Thread-6] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：7,等待任务: 0
2025-08-26 15:40:51.128 [async-task-pool195] INFO  c.r.f.a.e.PullFilterLogEvent - [lambda$1,90] - 开始获取IP阻断日志: 测试设备2
2025-08-26 15:40:51.128 [async-task-pool178] INFO  c.r.f.a.e.PullFilterLogEvent - [lambda$1,90] - 开始获取IP阻断日志: 测试非凡设备1
2025-08-26 15:40:51.141 [Thread-7] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:40:51.142 [Thread-7] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：9,等待任务: 0
2025-08-26 15:40:51.142 [async-task-pool199] INFO  c.r.f.a.e.PullFlowRiskAssetsEvent - [lambda$1,100] - 开始获取流量风险资产数据: 测试非凡设备1
2025-08-26 15:40:51.142 [async-task-pool146] INFO  c.r.f.a.e.PullFlowRiskAssetsEvent - [lambda$1,100] - 开始获取流量风险资产数据: 测试设备2
2025-08-26 15:40:51.143 [async-task-pool199] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,352] - 开始拉取流量风险资产数据，参数：FlowRiskAssetsParam(accessToken=null, riskType=all, startTime=2025-08-25 15:38:48, endTime=2025-08-26 15:38:51, page=1, pageSize=30, deviceConfigId=1)
2025-08-26 15:40:51.144 [async-task-pool146] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,352] - 开始拉取流量风险资产数据，参数：FlowRiskAssetsParam(accessToken=null, riskType=all, startTime=2025-08-25 15:38:48, endTime=2025-08-26 15:38:51, page=1, pageSize=30, deviceConfigId=4)
2025-08-26 15:40:51.145 [async-task-pool199] INFO  c.r.f.a.d.FlowRiskAssetsParam - [getRequestBase,49] - 非凡API流量风险资产请求参数: https://***************:23000/v2/flow-risk-assets?access_token=lst2BAzxJTQL0eSZ&risk_type=all&start_date=2025-08-25%2015%3A38%3A48&end_date=2025-08-26%2015%3A38%3A51&page=1&page_size=30
2025-08-26 15:40:51.160 [async-task-pool146] INFO  c.r.f.a.d.FlowRiskAssetsParam - [getRequestBase,49] - 非凡API流量风险资产请求参数: https://***************:23000/v2/flow-risk-assets?access_token=lst2BAzxJTQL0eSZ&risk_type=all&start_date=2025-08-25%2015%3A38%3A48&end_date=2025-08-26%2015%3A38%3A51&page=1&page_size=30
2025-08-26 15:40:51.169 [async-task-pool198] INFO  c.r.f.c.FFSafeRequestComponent - [execRequest,88] - 非凡请求响应: [{"id": 1058, "block_ip": "*************", "location": ["美国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-22 00:58:57", "release_time": "2025-08-29 01:00:59", "remarks": ""}, {"id": 1057, "block_ip": "************", "location": ["法国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-21 13:55:52", "release_time": "2025-08-28 13:56:53", "remarks": ""}, {"id": 1056, "block_ip": "***********", "location": ["印度"], "action": "阻断", "conditions": ["外网恶意攻击"], "type": "流量检测", "create_time": "2025-08-20 12:44:55", "release_time": "2025-08-27 12:46:57", "remarks": ""}]

2025-08-26 15:40:51.186 [async-task-pool190] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,987] - 主机入侵攻击总数: 0
2025-08-26 15:40:51.188 [async-task-pool190] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1010] - 第1页获取0条数据
2025-08-26 15:40:51.189 [async-task-pool190] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1014] - 当前页无数据，停止获取
2025-08-26 15:40:51.189 [async-task-pool190] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1083] - 拉取主机入侵攻击数据成功，但没有有效数据
2025-08-26 15:40:51.194 [async-task-pool175] INFO  c.r.f.c.FFSafeRequestComponent - [execRequest,88] - 非凡请求响应: [{"id": 1058, "block_ip": "*************", "location": ["美国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-22 00:58:57", "release_time": "2025-08-29 01:00:59", "remarks": ""}, {"id": 1057, "block_ip": "************", "location": ["法国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-21 13:55:52", "release_time": "2025-08-28 13:56:53", "remarks": ""}, {"id": 1056, "block_ip": "***********", "location": ["印度"], "action": "阻断", "conditions": ["外网恶意攻击"], "type": "流量检测", "create_time": "2025-08-20 12:44:55", "release_time": "2025-08-27 12:46:57", "remarks": ""}]

2025-08-26 15:40:51.206 [async-task-pool189] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,987] - 主机入侵攻击总数: 0
2025-08-26 15:40:51.206 [async-task-pool189] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1010] - 第1页获取0条数据
2025-08-26 15:40:51.207 [async-task-pool189] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1014] - 当前页无数据，停止获取
2025-08-26 15:40:51.208 [async-task-pool189] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1083] - 拉取主机入侵攻击数据成功，但没有有效数据
2025-08-26 15:40:51.211 [taskScheduler-39] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 103毫秒
2025-08-26 15:40:51.213 [Thread-8] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 106毫秒
2025-08-26 15:40:51.228 [Thread-6] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 101毫秒
2025-08-26 15:40:51.233 [async-task-pool146] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,458] - 流量风险资产总数: 6
2025-08-26 15:40:51.235 [async-task-pool146] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,481] - 第1页获取6条数据
2025-08-26 15:40:51.236 [async-task-pool146] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,488] - 当前页数据量(6)少于页大小(30)，已获取完所有数据
2025-08-26 15:40:51.255 [async-task-pool199] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,458] - 流量风险资产总数: 6
2025-08-26 15:40:51.255 [async-task-pool199] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,481] - 第1页获取6条数据
2025-08-26 15:40:51.256 [async-task-pool199] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,488] - 当前页数据量(6)少于页大小(30)，已获取完所有数据
2025-08-26 15:40:51.268 [async-task-pool146] INFO  c.r.m.c.c.FfsafeClientService - [processBatch,662] - 批量更新6条流量风险资产数据
2025-08-26 15:40:51.268 [async-task-pool146] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,551] - 拉取流量风险资产数据完成，耗时124ms，共1页，共6条数据，处理结果: true
2025-08-26 15:40:51.278 [async-task-pool199] INFO  c.r.m.c.c.FfsafeClientService - [processBatch,662] - 批量更新6条流量风险资产数据
2025-08-26 15:40:51.278 [async-task-pool199] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,551] - 拉取流量风险资产数据完成，耗时135ms，共1页，共6条数据，处理结果: true
2025-08-26 15:40:51.287 [Thread-7] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 146毫秒
2025-08-26 15:40:59.156 [Thread-5] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:40:59.157 [Thread-5] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：2,等待任务: 0
2025-08-26 15:40:59.157 [async-task-pool6] INFO  c.r.f.a.e.PullDataEvent - [lambda$1,102] - 开始流量告警日志同步: 测试非凡设备1
2025-08-26 15:40:59.157 [async-task-pool0] INFO  c.r.f.a.e.PullDataEvent - [lambda$1,102] - 开始流量告警日志同步: 测试设备2
2025-08-26 15:40:59.342 [Thread-5] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 186毫秒
2025-08-26 15:41:00.230 [pool-4-thread-1] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [lambda$0,81] - 最大连接：200 当前连接: 3
2025-08-26 15:41:01.244 [Thread-6] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:41:01.245 [Thread-6] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：1,等待任务: 0
2025-08-26 15:41:01.247 [async-task-pool184] INFO  c.r.f.a.e.PullFilterLogEvent - [lambda$1,90] - 开始获取IP阻断日志: 测试设备2
2025-08-26 15:41:01.247 [async-task-pool193] INFO  c.r.f.a.e.PullFilterLogEvent - [lambda$1,90] - 开始获取IP阻断日志: 测试非凡设备1
2025-08-26 15:41:01.263 [Thread-8] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:41:01.263 [taskScheduler-39] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:41:01.264 [Thread-8] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：3,等待任务: 0
2025-08-26 15:41:01.264 [taskScheduler-39] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：3,等待任务: 0
2025-08-26 15:41:01.264 [async-task-pool1] INFO  c.r.f.a.e.PullHostIntrusionAttackEvent - [lambda$1,83] - 开始获取主机入侵攻击数据: 测试非凡设备1
2025-08-26 15:41:01.264 [async-task-pool3] INFO  c.r.f.a.e.PullHostIntrusionAttackEvent - [lambda$1,83] - 开始获取主机入侵攻击数据: 测试设备2
2025-08-26 15:41:01.269 [async-task-pool1] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,874] - 开始拉取主机入侵攻击数据，参数：HostIntrusionAttackParam(accessToken=null, sip=null, dip=null, startDate=2025-08-25 15:38:51, endDate=2025-08-26 15:39:01, page=1, pSize=20, deviceConfigId=1)
2025-08-26 15:41:01.271 [async-task-pool3] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,874] - 开始拉取主机入侵攻击数据，参数：HostIntrusionAttackParam(accessToken=null, sip=null, dip=null, startDate=2025-08-25 15:38:51, endDate=2025-08-26 15:39:01, page=1, pSize=20, deviceConfigId=4)
2025-08-26 15:41:01.273 [async-task-pool1] INFO  c.r.f.a.d.HostIntrusionAttackParam - [getRequestBase,80] - 非凡API主机入侵攻击分页请求参数: https://***************:23000/v2/host-edr-intrusion?access_token=lst2BAzxJTQL0eSZ&start_date=2025-08-25%2015%3A38%3A51&end_date=2025-08-26%2015%3A39%3A01&page=1&p_size=20
2025-08-26 15:41:01.275 [async-task-pool3] INFO  c.r.f.a.d.HostIntrusionAttackParam - [getRequestBase,80] - 非凡API主机入侵攻击分页请求参数: https://***************:23000/v2/host-edr-intrusion?access_token=lst2BAzxJTQL0eSZ&start_date=2025-08-25%2015%3A38%3A51&end_date=2025-08-26%2015%3A39%3A01&page=1&p_size=20
2025-08-26 15:41:01.337 [Thread-7] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:41:01.338 [Thread-7] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：7,等待任务: 0
2025-08-26 15:41:01.338 [async-task-pool11] INFO  c.r.f.a.e.PullFlowRiskAssetsEvent - [lambda$1,100] - 开始获取流量风险资产数据: 测试非凡设备1
2025-08-26 15:41:01.338 [async-task-pool155] INFO  c.r.f.a.e.PullFlowRiskAssetsEvent - [lambda$1,100] - 开始获取流量风险资产数据: 测试设备2
2025-08-26 15:41:01.369 [async-task-pool11] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,352] - 开始拉取流量风险资产数据，参数：FlowRiskAssetsParam(accessToken=null, riskType=all, startTime=2025-08-25 15:38:58, endTime=2025-08-26 15:39:01, page=1, pageSize=30, deviceConfigId=1)
2025-08-26 15:41:01.370 [async-task-pool155] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,352] - 开始拉取流量风险资产数据，参数：FlowRiskAssetsParam(accessToken=null, riskType=all, startTime=2025-08-25 15:38:58, endTime=2025-08-26 15:39:01, page=1, pageSize=30, deviceConfigId=4)
2025-08-26 15:41:01.375 [async-task-pool11] INFO  c.r.f.a.d.FlowRiskAssetsParam - [getRequestBase,49] - 非凡API流量风险资产请求参数: https://***************:23000/v2/flow-risk-assets?access_token=lst2BAzxJTQL0eSZ&risk_type=all&start_date=2025-08-25%2015%3A38%3A58&end_date=2025-08-26%2015%3A39%3A01&page=1&page_size=30
2025-08-26 15:41:01.378 [async-task-pool155] INFO  c.r.f.a.d.FlowRiskAssetsParam - [getRequestBase,49] - 非凡API流量风险资产请求参数: https://***************:23000/v2/flow-risk-assets?access_token=lst2BAzxJTQL0eSZ&risk_type=all&start_date=2025-08-25%2015%3A38%3A58&end_date=2025-08-26%2015%3A39%3A01&page=1&page_size=30
2025-08-26 15:41:01.464 [async-task-pool8] INFO  c.r.f.c.FFSafeRequestComponent - [execRequest,88] - 非凡请求响应: [{"id": 1058, "block_ip": "*************", "location": ["美国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-22 00:58:57", "release_time": "2025-08-29 01:00:59", "remarks": ""}, {"id": 1057, "block_ip": "************", "location": ["法国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-21 13:55:52", "release_time": "2025-08-28 13:56:53", "remarks": ""}, {"id": 1056, "block_ip": "***********", "location": ["印度"], "action": "阻断", "conditions": ["外网恶意攻击"], "type": "流量检测", "create_time": "2025-08-20 12:44:55", "release_time": "2025-08-27 12:46:57", "remarks": ""}]

2025-08-26 15:41:01.467 [async-task-pool191] INFO  c.r.f.c.FFSafeRequestComponent - [execRequest,88] - 非凡请求响应: [{"id": 1058, "block_ip": "*************", "location": ["美国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-22 00:58:57", "release_time": "2025-08-29 01:00:59", "remarks": ""}, {"id": 1057, "block_ip": "************", "location": ["法国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-21 13:55:52", "release_time": "2025-08-28 13:56:53", "remarks": ""}, {"id": 1056, "block_ip": "***********", "location": ["印度"], "action": "阻断", "conditions": ["外网恶意攻击"], "type": "流量检测", "create_time": "2025-08-20 12:44:55", "release_time": "2025-08-27 12:46:57", "remarks": ""}]

2025-08-26 15:41:01.469 [async-task-pool3] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,987] - 主机入侵攻击总数: 0
2025-08-26 15:41:01.472 [async-task-pool3] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1010] - 第1页获取0条数据
2025-08-26 15:41:01.472 [async-task-pool3] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1014] - 当前页无数据，停止获取
2025-08-26 15:41:01.478 [async-task-pool3] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1083] - 拉取主机入侵攻击数据成功，但没有有效数据
2025-08-26 15:41:01.484 [async-task-pool1] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,987] - 主机入侵攻击总数: 0
2025-08-26 15:41:01.485 [async-task-pool1] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1010] - 第1页获取0条数据
2025-08-26 15:41:01.485 [async-task-pool1] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1014] - 当前页无数据，停止获取
2025-08-26 15:41:01.485 [async-task-pool1] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1083] - 拉取主机入侵攻击数据成功，但没有有效数据
2025-08-26 15:41:01.486 [async-task-pool155] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,458] - 流量风险资产总数: 6
2025-08-26 15:41:01.487 [async-task-pool155] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,481] - 第1页获取6条数据
2025-08-26 15:41:01.487 [taskScheduler-39] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 224毫秒
2025-08-26 15:41:01.487 [async-task-pool155] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,488] - 当前页数据量(6)少于页大小(30)，已获取完所有数据
2025-08-26 15:41:01.496 [async-task-pool11] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,458] - 流量风险资产总数: 6
2025-08-26 15:41:01.498 [async-task-pool11] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,481] - 第1页获取6条数据
2025-08-26 15:41:01.499 [async-task-pool11] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,488] - 当前页数据量(6)少于页大小(30)，已获取完所有数据
2025-08-26 15:41:01.551 [Thread-8] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 288毫秒
2025-08-26 15:41:01.551 [async-task-pool155] INFO  c.r.m.c.c.FfsafeClientService - [processBatch,662] - 批量更新6条流量风险资产数据
2025-08-26 15:41:01.552 [async-task-pool155] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,551] - 拉取流量风险资产数据完成，耗时182ms，共1页，共6条数据，处理结果: true
2025-08-26 15:41:01.558 [async-task-pool11] INFO  c.r.m.c.c.FfsafeClientService - [processBatch,662] - 批量更新6条流量风险资产数据
2025-08-26 15:41:01.559 [async-task-pool11] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,551] - 拉取流量风险资产数据完成，耗时190ms，共1页，共6条数据，处理结果: true
2025-08-26 15:41:01.563 [Thread-6] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 319毫秒
2025-08-26 15:41:01.569 [Thread-7] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 232毫秒
2025-08-26 15:41:09.348 [Thread-5] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:41:09.349 [Thread-5] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：1,等待任务: 0
2025-08-26 15:41:09.350 [async-task-pool2] INFO  c.r.f.a.e.PullDataEvent - [lambda$1,102] - 开始流量告警日志同步: 测试非凡设备1
2025-08-26 15:41:09.350 [async-task-pool179] INFO  c.r.f.a.e.PullDataEvent - [lambda$1,102] - 开始流量告警日志同步: 测试设备2
2025-08-26 15:41:09.519 [Thread-5] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 171毫秒
2025-08-26 15:41:10.325 [pool-4-thread-1] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [lambda$0,81] - 最大连接：200 当前连接: 2
2025-08-26 15:41:11.535 [taskScheduler-39] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:41:11.536 [taskScheduler-39] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：1,等待任务: 0
2025-08-26 15:41:11.605 [async-task-pool187] INFO  c.r.f.c.FFSafeRequestComponent - [execRequest,88] - 非凡请求响应: [{"id": 1058, "block_ip": "*************", "location": ["美国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-22 00:58:57", "release_time": "2025-08-29 01:00:59", "remarks": ""}, {"id": 1057, "block_ip": "************", "location": ["法国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-21 13:55:52", "release_time": "2025-08-28 13:56:53", "remarks": ""}, {"id": 1056, "block_ip": "***********", "location": ["印度"], "action": "阻断", "conditions": ["外网恶意攻击"], "type": "流量检测", "create_time": "2025-08-20 12:44:55", "release_time": "2025-08-27 12:46:57", "remarks": ""}]

2025-08-26 15:41:11.611 [async-task-pool9] INFO  c.r.f.c.FFSafeRequestComponent - [execRequest,88] - 非凡请求响应: [{"id": 1058, "block_ip": "*************", "location": ["美国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-22 00:58:57", "release_time": "2025-08-29 01:00:59", "remarks": ""}, {"id": 1057, "block_ip": "************", "location": ["法国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-21 13:55:52", "release_time": "2025-08-28 13:56:53", "remarks": ""}, {"id": 1056, "block_ip": "***********", "location": ["印度"], "action": "阻断", "conditions": ["外网恶意攻击"], "type": "流量检测", "create_time": "2025-08-20 12:44:55", "release_time": "2025-08-27 12:46:57", "remarks": ""}]

2025-08-26 15:41:11.695 [Thread-8] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:41:11.695 [Thread-8] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：3,等待任务: 0
2025-08-26 15:41:11.700 [async-task-pool165] INFO  c.r.f.a.e.PullHostIntrusionAttackEvent - [lambda$1,83] - 开始获取主机入侵攻击数据: 测试非凡设备1
2025-08-26 15:41:11.700 [async-task-pool173] INFO  c.r.f.a.e.PullHostIntrusionAttackEvent - [lambda$1,83] - 开始获取主机入侵攻击数据: 测试设备2
2025-08-26 15:41:11.703 [async-task-pool173] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,874] - 开始拉取主机入侵攻击数据，参数：HostIntrusionAttackParam(accessToken=null, sip=null, dip=null, startDate=2025-08-25 15:39:01, endDate=2025-08-26 15:39:11, page=1, pSize=20, deviceConfigId=4)
2025-08-26 15:41:11.702 [async-task-pool165] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,874] - 开始拉取主机入侵攻击数据，参数：HostIntrusionAttackParam(accessToken=null, sip=null, dip=null, startDate=2025-08-25 15:39:01, endDate=2025-08-26 15:39:11, page=1, pSize=20, deviceConfigId=1)
2025-08-26 15:41:11.707 [async-task-pool173] INFO  c.r.f.a.d.HostIntrusionAttackParam - [getRequestBase,80] - 非凡API主机入侵攻击分页请求参数: https://***************:23000/v2/host-edr-intrusion?access_token=lst2BAzxJTQL0eSZ&start_date=2025-08-25%2015%3A39%3A01&end_date=2025-08-26%2015%3A39%3A11&page=1&p_size=20
2025-08-26 15:41:11.708 [async-task-pool165] INFO  c.r.f.a.d.HostIntrusionAttackParam - [getRequestBase,80] - 非凡API主机入侵攻击分页请求参数: https://***************:23000/v2/host-edr-intrusion?access_token=lst2BAzxJTQL0eSZ&start_date=2025-08-25%2015%3A39%3A01&end_date=2025-08-26%2015%3A39%3A11&page=1&p_size=20
2025-08-26 15:41:11.811 [Thread-7] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:41:11.811 [Thread-6] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:41:11.812 [Thread-7] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：5,等待任务: 0
2025-08-26 15:41:11.813 [Thread-6] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：5,等待任务: 0
2025-08-26 15:41:11.813 [async-task-pool15] INFO  c.r.f.a.e.PullFlowRiskAssetsEvent - [lambda$1,100] - 开始获取流量风险资产数据: 测试非凡设备1
2025-08-26 15:41:11.813 [async-task-pool18] INFO  c.r.f.a.e.PullFlowRiskAssetsEvent - [lambda$1,100] - 开始获取流量风险资产数据: 测试设备2
2025-08-26 15:41:11.814 [async-task-pool17] INFO  c.r.f.a.e.PullFilterLogEvent - [lambda$1,90] - 开始获取IP阻断日志: 测试设备2
2025-08-26 15:41:11.814 [async-task-pool20] INFO  c.r.f.a.e.PullFilterLogEvent - [lambda$1,90] - 开始获取IP阻断日志: 测试非凡设备1
2025-08-26 15:41:11.814 [async-task-pool15] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,352] - 开始拉取流量风险资产数据，参数：FlowRiskAssetsParam(accessToken=null, riskType=all, startTime=2025-08-25 15:39:08, endTime=2025-08-26 15:39:11, page=1, pageSize=30, deviceConfigId=1)
2025-08-26 15:41:11.814 [async-task-pool18] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,352] - 开始拉取流量风险资产数据，参数：FlowRiskAssetsParam(accessToken=null, riskType=all, startTime=2025-08-25 15:39:08, endTime=2025-08-26 15:39:11, page=1, pageSize=30, deviceConfigId=4)
2025-08-26 15:41:11.862 [async-task-pool15] INFO  c.r.f.a.d.FlowRiskAssetsParam - [getRequestBase,49] - 非凡API流量风险资产请求参数: https://***************:23000/v2/flow-risk-assets?access_token=lst2BAzxJTQL0eSZ&risk_type=all&start_date=2025-08-25%2015%3A39%3A08&end_date=2025-08-26%2015%3A39%3A11&page=1&page_size=30
2025-08-26 15:41:11.863 [async-task-pool18] INFO  c.r.f.a.d.FlowRiskAssetsParam - [getRequestBase,49] - 非凡API流量风险资产请求参数: https://***************:23000/v2/flow-risk-assets?access_token=lst2BAzxJTQL0eSZ&risk_type=all&start_date=2025-08-25%2015%3A39%3A08&end_date=2025-08-26%2015%3A39%3A11&page=1&page_size=30
2025-08-26 15:41:11.967 [taskScheduler-39] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 432毫秒
2025-08-26 15:41:11.980 [async-task-pool173] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,987] - 主机入侵攻击总数: 0
2025-08-26 15:41:11.982 [async-task-pool165] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,987] - 主机入侵攻击总数: 0
2025-08-26 15:41:11.985 [async-task-pool173] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1010] - 第1页获取0条数据
2025-08-26 15:41:11.986 [async-task-pool165] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1010] - 第1页获取0条数据
2025-08-26 15:41:11.986 [async-task-pool173] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1014] - 当前页无数据，停止获取
2025-08-26 15:41:11.986 [async-task-pool165] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1014] - 当前页无数据，停止获取
2025-08-26 15:41:11.986 [async-task-pool173] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1083] - 拉取主机入侵攻击数据成功，但没有有效数据
2025-08-26 15:41:11.987 [async-task-pool165] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1083] - 拉取主机入侵攻击数据成功，但没有有效数据
2025-08-26 15:41:12.006 [Thread-8] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 311毫秒
2025-08-26 15:41:12.065 [async-task-pool18] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,458] - 流量风险资产总数: 6
2025-08-26 15:41:12.088 [async-task-pool18] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,481] - 第1页获取6条数据
2025-08-26 15:41:12.088 [async-task-pool15] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,458] - 流量风险资产总数: 6
2025-08-26 15:41:12.090 [async-task-pool18] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,488] - 当前页数据量(6)少于页大小(30)，已获取完所有数据
2025-08-26 15:41:12.090 [async-task-pool15] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,481] - 第1页获取6条数据
2025-08-26 15:41:12.091 [async-task-pool15] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,488] - 当前页数据量(6)少于页大小(30)，已获取完所有数据
2025-08-26 15:41:12.134 [Thread-6] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 323毫秒
2025-08-26 15:41:12.138 [async-task-pool15] INFO  c.r.m.c.c.FfsafeClientService - [processBatch,662] - 批量更新6条流量风险资产数据
2025-08-26 15:41:12.138 [async-task-pool15] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,551] - 拉取流量风险资产数据完成，耗时324ms，共1页，共6条数据，处理结果: true
2025-08-26 15:41:12.141 [async-task-pool18] INFO  c.r.m.c.c.FfsafeClientService - [processBatch,662] - 批量更新6条流量风险资产数据
2025-08-26 15:41:12.141 [async-task-pool18] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,551] - 拉取流量风险资产数据完成，耗时327ms，共1页，共6条数据，处理结果: true
2025-08-26 15:41:12.146 [Thread-7] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 335毫秒
2025-08-26 15:41:14.606 [pool-6-thread-4] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 27秒267毫秒
2025-08-26 15:41:14.924 [pool-6-thread-4] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sync,255] - 批量更新攻击者视角告警列开始: 13
2025-08-26 15:41:14.957 [pool-6-thread-4] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=**************, operationType=UPDATE
2025-08-26 15:41:14.958 [pool-6-thread-4] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=**************, operationType=UPDATE
2025-08-26 15:41:14.960 [pool-6-thread-4] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=**************, operationType=UPDATE
2025-08-26 15:41:14.962 [pool-6-thread-4] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=**********0, operationType=UPDATE
2025-08-26 15:41:14.964 [pool-6-thread-4] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=*************, operationType=UPDATE
2025-08-26 15:41:14.965 [pool-6-thread-4] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=**************, operationType=UPDATE
2025-08-26 15:41:14.966 [pool-6-thread-4] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=*************, operationType=UPDATE
2025-08-26 15:41:14.968 [pool-6-thread-4] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=************, operationType=UPDATE
2025-08-26 15:41:14.973 [pool-6-thread-4] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=***********, operationType=UPDATE
2025-08-26 15:41:14.979 [pool-6-thread-4] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=***************, operationType=UPDATE
2025-08-26 15:41:14.982 [pool-6-thread-4] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=***********, operationType=UPDATE
2025-08-26 15:41:14.984 [pool-6-thread-4] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=**************, operationType=UPDATE
2025-08-26 15:41:14.988 [pool-6-thread-4] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=***********, operationType=UPDATE
2025-08-26 15:41:14.991 [pool-6-thread-3] INFO  c.r.s.t.FfSafeFlowAlarmTask - [syncTask,46] - 开始同步攻击者告警数据
2025-08-26 15:41:15.058 [pool-6-thread-3] INFO  c.r.f.c.FFSafeRequestComponent - [execRequest,88] - 非凡请求响应: [{"attack_ip": "************", "risk_level": 3, "ip_tags": ["内网恶意攻击"], "location": "局域网", "victim_ip_nums": 5, "attack_type_nums": 6, "attack_nums": 97, "start_time": "2025-08-25 16:17:50", "update_time": "2025-08-26 15:37:44"}, {"attack_ip": "***********", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 1, "attack_type_nums": 1, "attack_nums": 239, "start_time": "2025-08-26 09:54:16", "update_time": "2025-08-26 15:37:42"}, {"attack_ip": "***************", "risk_level": 3, "ip_tags": ["内网端口扫描", "内网恶意攻击", "暴力破解"], "location": "局域网", "victim_ip_nums": 30, "attack_type_nums": 1079, "attack_nums": 58440, "start_time": "2025-08-26 14:58:22", "update_time": "2025-08-26 15:34:10"}, {"attack_ip": "***********", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 2, "attack_type_nums": 2, "attack_nums": 155, "start_time": "2025-08-25 13:27:18", "update_time": "2025-08-26 15:33:16"}, {"attack_ip": "**************", "risk_level": 3, "ip_tags": ["内网端口扫描", "内网恶意攻击", "暴力破解"], "location": "局域网", "victim_ip_nums": 257, "attack_type_nums": 62, "attack_nums": 1176, "start_time": "2025-08-26 14:24:32", "update_time": "2025-08-26 15:32:50"}, {"attack_ip": "***********", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 1, "attack_type_nums": 3, "attack_nums": 106, "start_time": "2025-08-26 09:49:05", "update_time": "2025-08-26 15:31:50"}, {"attack_ip": "***********", "risk_level": 2, "ip_tags": ["踩中蜜罐", "内网可疑行为"], "location": "局域网", "victim_ip_nums": 2, "attack_type_nums": 6, "attack_nums": 115, "start_time": "2025-08-25 14:42:32", "update_time": "2025-08-26 15:30:35"}, {"attack_ip": "**********", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 1, "attack_type_nums": 1, "attack_nums": 403, "start_time": "2025-08-25 08:14:50", "update_time": "2025-08-26 15:29:54"}, {"attack_ip": "***********", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 1, "attack_type_nums": 1, "attack_nums": 277, "start_time": "2025-08-25 08:27:55", "update_time": "2025-08-26 15:29:41"}]

2025-08-26 15:41:15.063 [pool-6-thread-3] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:9...
2025-08-26 15:41:15.077 [pool-6-thread-3] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：0,等待任务: 0
2025-08-26 15:41:19.889 [Thread-5] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:41:19.891 [Thread-5] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：1,等待任务: 0
2025-08-26 15:41:19.891 [async-task-pool28] INFO  c.r.f.a.e.PullDataEvent - [lambda$1,102] - 开始流量告警日志同步: 测试设备2
2025-08-26 15:41:19.891 [async-task-pool26] INFO  c.r.f.a.e.PullDataEvent - [lambda$1,102] - 开始流量告警日志同步: 测试非凡设备1
2025-08-26 15:41:20.078 [Thread-5] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 189毫秒
2025-08-26 15:41:20.558 [pool-4-thread-1] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [lambda$0,81] - 最大连接：200 当前连接: 2
2025-08-26 15:41:22.022 [taskScheduler-39] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:41:22.022 [taskScheduler-39] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：1,等待任务: 0
2025-08-26 15:41:22.039 [Thread-8] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:41:22.043 [Thread-8] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：3,等待任务: 0
2025-08-26 15:41:22.045 [async-task-pool4] INFO  c.r.f.a.e.PullHostIntrusionAttackEvent - [lambda$1,83] - 开始获取主机入侵攻击数据: 测试设备2
2025-08-26 15:41:22.045 [async-task-pool16] INFO  c.r.f.a.e.PullHostIntrusionAttackEvent - [lambda$1,83] - 开始获取主机入侵攻击数据: 测试非凡设备1
2025-08-26 15:41:22.047 [async-task-pool4] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,874] - 开始拉取主机入侵攻击数据，参数：HostIntrusionAttackParam(accessToken=null, sip=null, dip=null, startDate=2025-08-25 15:39:11, endDate=2025-08-26 15:39:22, page=1, pSize=20, deviceConfigId=4)
2025-08-26 15:41:22.050 [async-task-pool16] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,874] - 开始拉取主机入侵攻击数据，参数：HostIntrusionAttackParam(accessToken=null, sip=null, dip=null, startDate=2025-08-25 15:39:11, endDate=2025-08-26 15:39:22, page=1, pSize=20, deviceConfigId=1)
2025-08-26 15:41:22.052 [async-task-pool4] INFO  c.r.f.a.d.HostIntrusionAttackParam - [getRequestBase,80] - 非凡API主机入侵攻击分页请求参数: https://***************:23000/v2/host-edr-intrusion?access_token=lst2BAzxJTQL0eSZ&start_date=2025-08-25%2015%3A39%3A11&end_date=2025-08-26%2015%3A39%3A22&page=1&p_size=20
2025-08-26 15:41:22.052 [async-task-pool16] INFO  c.r.f.a.d.HostIntrusionAttackParam - [getRequestBase,80] - 非凡API主机入侵攻击分页请求参数: https://***************:23000/v2/host-edr-intrusion?access_token=lst2BAzxJTQL0eSZ&start_date=2025-08-25%2015%3A39%3A11&end_date=2025-08-26%2015%3A39%3A22&page=1&p_size=20
2025-08-26 15:41:22.143 [async-task-pool4] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,987] - 主机入侵攻击总数: 0
2025-08-26 15:41:22.144 [async-task-pool4] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1010] - 第1页获取0条数据
2025-08-26 15:41:22.145 [async-task-pool12] INFO  c.r.f.c.FFSafeRequestComponent - [execRequest,88] - 非凡请求响应: [{"id": 1058, "block_ip": "*************", "location": ["美国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-22 00:58:57", "release_time": "2025-08-29 01:00:59", "remarks": ""}, {"id": 1057, "block_ip": "************", "location": ["法国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-21 13:55:52", "release_time": "2025-08-28 13:56:53", "remarks": ""}, {"id": 1056, "block_ip": "***********", "location": ["印度"], "action": "阻断", "conditions": ["外网恶意攻击"], "type": "流量检测", "create_time": "2025-08-20 12:44:55", "release_time": "2025-08-27 12:46:57", "remarks": ""}]

2025-08-26 15:41:22.145 [async-task-pool4] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1014] - 当前页无数据，停止获取
2025-08-26 15:41:22.148 [async-task-pool4] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1083] - 拉取主机入侵攻击数据成功，但没有有效数据
2025-08-26 15:41:22.157 [async-task-pool194] INFO  c.r.f.c.FFSafeRequestComponent - [execRequest,88] - 非凡请求响应: [{"id": 1058, "block_ip": "*************", "location": ["美国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-22 00:58:57", "release_time": "2025-08-29 01:00:59", "remarks": ""}, {"id": 1057, "block_ip": "************", "location": ["法国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-21 13:55:52", "release_time": "2025-08-28 13:56:53", "remarks": ""}, {"id": 1056, "block_ip": "***********", "location": ["印度"], "action": "阻断", "conditions": ["外网恶意攻击"], "type": "流量检测", "create_time": "2025-08-20 12:44:55", "release_time": "2025-08-27 12:46:57", "remarks": ""}]

2025-08-26 15:41:22.155 [async-task-pool16] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,987] - 主机入侵攻击总数: 0
2025-08-26 15:41:22.162 [Thread-6] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:41:22.163 [async-task-pool16] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1010] - 第1页获取0条数据
2025-08-26 15:41:22.164 [Thread-6] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：5,等待任务: 0
2025-08-26 15:41:22.164 [async-task-pool16] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1014] - 当前页无数据，停止获取
2025-08-26 15:41:22.165 [async-task-pool38] INFO  c.r.f.a.e.PullFilterLogEvent - [lambda$1,90] - 开始获取IP阻断日志: 测试非凡设备1
2025-08-26 15:41:22.165 [async-task-pool37] INFO  c.r.f.a.e.PullFilterLogEvent - [lambda$1,90] - 开始获取IP阻断日志: 测试设备2
2025-08-26 15:41:22.165 [async-task-pool16] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1083] - 拉取主机入侵攻击数据成功，但没有有效数据
2025-08-26 15:41:22.165 [Thread-7] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:41:22.168 [Thread-7] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：7,等待任务: 0
2025-08-26 15:41:22.169 [async-task-pool43] INFO  c.r.f.a.e.PullFlowRiskAssetsEvent - [lambda$1,100] - 开始获取流量风险资产数据: 测试设备2
2025-08-26 15:41:22.169 [async-task-pool174] INFO  c.r.f.a.e.PullFlowRiskAssetsEvent - [lambda$1,100] - 开始获取流量风险资产数据: 测试非凡设备1
2025-08-26 15:41:22.171 [async-task-pool43] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,352] - 开始拉取流量风险资产数据，参数：FlowRiskAssetsParam(accessToken=null, riskType=all, startTime=2025-08-25 15:39:18, endTime=2025-08-26 15:39:22, page=1, pageSize=30, deviceConfigId=4)
2025-08-26 15:41:22.171 [async-task-pool174] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,352] - 开始拉取流量风险资产数据，参数：FlowRiskAssetsParam(accessToken=null, riskType=all, startTime=2025-08-25 15:39:18, endTime=2025-08-26 15:39:22, page=1, pageSize=30, deviceConfigId=1)
2025-08-26 15:41:22.178 [async-task-pool43] INFO  c.r.f.a.d.FlowRiskAssetsParam - [getRequestBase,49] - 非凡API流量风险资产请求参数: https://***************:23000/v2/flow-risk-assets?access_token=lst2BAzxJTQL0eSZ&risk_type=all&start_date=2025-08-25%2015%3A39%3A18&end_date=2025-08-26%2015%3A39%3A22&page=1&page_size=30
2025-08-26 15:41:22.179 [async-task-pool174] INFO  c.r.f.a.d.FlowRiskAssetsParam - [getRequestBase,49] - 非凡API流量风险资产请求参数: https://***************:23000/v2/flow-risk-assets?access_token=lst2BAzxJTQL0eSZ&risk_type=all&start_date=2025-08-25%2015%3A39%3A18&end_date=2025-08-26%2015%3A39%3A22&page=1&page_size=30
2025-08-26 15:41:22.189 [Thread-8] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 150毫秒
2025-08-26 15:41:22.198 [taskScheduler-39] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 176毫秒
2025-08-26 15:41:22.269 [async-task-pool174] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,458] - 流量风险资产总数: 6
2025-08-26 15:41:22.269 [async-task-pool43] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,458] - 流量风险资产总数: 6
2025-08-26 15:41:22.271 [async-task-pool174] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,481] - 第1页获取6条数据
2025-08-26 15:41:22.272 [async-task-pool43] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,481] - 第1页获取6条数据
2025-08-26 15:41:22.272 [async-task-pool174] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,488] - 当前页数据量(6)少于页大小(30)，已获取完所有数据
2025-08-26 15:41:22.272 [async-task-pool43] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,488] - 当前页数据量(6)少于页大小(30)，已获取完所有数据
2025-08-26 15:41:22.283 [Thread-6] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 121毫秒
2025-08-26 15:41:22.302 [async-task-pool43] INFO  c.r.m.c.c.FfsafeClientService - [processBatch,662] - 批量更新6条流量风险资产数据
2025-08-26 15:41:22.302 [async-task-pool43] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,551] - 拉取流量风险资产数据完成，耗时131ms，共1页，共6条数据，处理结果: true
2025-08-26 15:41:22.309 [async-task-pool174] INFO  c.r.m.c.c.FfsafeClientService - [processBatch,662] - 批量更新6条流量风险资产数据
2025-08-26 15:41:22.310 [async-task-pool174] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,551] - 拉取流量风险资产数据完成，耗时139ms，共1页，共6条数据，处理结果: true
2025-08-26 15:41:22.316 [Thread-7] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 151毫秒
2025-08-26 15:41:28.592 [pool-6-thread-3] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 13秒529毫秒
2025-08-26 15:41:28.877 [pool-6-thread-3] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sync,255] - 批量更新攻击者视角告警列开始: 9
2025-08-26 15:41:28.903 [pool-6-thread-3] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=************, operationType=UPDATE
2025-08-26 15:41:28.904 [pool-6-thread-3] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=***********, operationType=UPDATE
2025-08-26 15:41:28.906 [pool-6-thread-3] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=***************, operationType=UPDATE
2025-08-26 15:41:28.907 [pool-6-thread-3] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=***********, operationType=UPDATE
2025-08-26 15:41:28.908 [pool-6-thread-3] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=**************, operationType=UPDATE
2025-08-26 15:41:28.909 [pool-6-thread-3] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=***********, operationType=UPDATE
2025-08-26 15:41:28.910 [pool-6-thread-3] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=***********, operationType=UPDATE
2025-08-26 15:41:28.912 [pool-6-thread-3] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=**********, operationType=UPDATE
2025-08-26 15:41:28.916 [pool-6-thread-3] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=***********, operationType=UPDATE
2025-08-26 15:41:28.919 [pool-6-thread-4] INFO  c.r.s.t.FfSafeFlowAlarmTask - [syncTask,46] - 开始同步攻击者告警数据
2025-08-26 15:41:28.981 [pool-6-thread-4] INFO  c.r.f.c.FFSafeRequestComponent - [execRequest,88] - 非凡请求响应: [{"attack_ip": "**************", "risk_level": 3, "ip_tags": ["非法外联"], "location": "局域网", "victim_ip_nums": 2, "attack_type_nums": 2, "attack_nums": 176129, "start_time": "2025-08-25 08:00:04", "update_time": "2025-08-26 15:41:23"}, {"attack_ip": "**************", "risk_level": 3, "ip_tags": ["暴力破解"], "location": "局域网", "victim_ip_nums": 9, "attack_type_nums": 9, "attack_nums": 14927, "start_time": "2025-08-25 08:00:02", "update_time": "2025-08-26 15:41:15"}, {"attack_ip": "**************", "risk_level": 2, "ip_tags": ["弱口令登录成功"], "location": "局域网", "victim_ip_nums": 2, "attack_type_nums": 5, "attack_nums": 7380, "start_time": "2025-08-25 08:00:02", "update_time": "2025-08-26 15:41:10"}, {"attack_ip": "**********0", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 1, "attack_type_nums": 1, "attack_nums": 585, "start_time": "2025-08-25 08:59:16", "update_time": "2025-08-26 15:40:01"}, {"attack_ip": "*************", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 2, "attack_type_nums": 1, "attack_nums": 333, "start_time": "2025-08-25 08:03:53", "update_time": "2025-08-26 15:39:58"}, {"attack_ip": "**************", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 1, "attack_type_nums": 1, "attack_nums": 14, "start_time": "2025-08-26 11:06:34", "update_time": "2025-08-26 15:39:35"}, {"attack_ip": "*************", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 2, "attack_type_nums": 1, "attack_nums": 336, "start_time": "2025-08-25 08:02:29", "update_time": "2025-08-26 15:38:37"}, {"attack_ip": "************", "risk_level": 3, "ip_tags": ["内网恶意攻击"], "location": "局域网", "victim_ip_nums": 5, "attack_type_nums": 6, "attack_nums": 97, "start_time": "2025-08-25 16:17:50", "update_time": "2025-08-26 15:37:44"}, {"attack_ip": "***********", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 1, "attack_type_nums": 1, "attack_nums": 239, "start_time": "2025-08-26 09:54:16", "update_time": "2025-08-26 15:37:42"}, {"attack_ip": "***************", "risk_level": 3, "ip_tags": ["内网端口扫描", "内网恶意攻击", "暴力破解"], "location": "局域网", "victim_ip_nums": 30, "attack_type_nums": 1079, "attack_nums": 58440, "start_time": "2025-08-26 14:58:22", "update_time": "2025-08-26 15:34:10"}, {"attack_ip": "***********", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 2, "attack_type_nums": 2, "attack_nums": 155, "start_time": "2025-08-25 13:27:18", "update_time": "2025-08-26 15:33:16"}, {"attack_ip": "**************", "risk_level": 3, "ip_tags": ["内网端口扫描", "内网恶意攻击", "暴力破解"], "location": "局域网", "victim_ip_nums": 257, "attack_type_nums": 62, "attack_nums": 1176, "start_time": "2025-08-26 14:24:32", "update_time": "2025-08-26 15:32:50"}]

2025-08-26 15:41:28.991 [pool-6-thread-4] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:12...
2025-08-26 15:41:28.991 [pool-6-thread-4] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：0,等待任务: 0
2025-08-26 15:41:30.093 [Thread-5] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:41:30.094 [Thread-5] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：4,等待任务: 0
2025-08-26 15:41:30.094 [async-task-pool36] INFO  c.r.f.a.e.PullDataEvent - [lambda$1,102] - 开始流量告警日志同步: 测试设备2
2025-08-26 15:41:30.094 [async-task-pool29] INFO  c.r.f.a.e.PullDataEvent - [lambda$1,102] - 开始流量告警日志同步: 测试非凡设备1
2025-08-26 15:41:30.513 [Thread-5] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 420毫秒
2025-08-26 15:41:30.563 [pool-4-thread-1] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [lambda$0,81] - 最大连接：200 当前连接: 5
2025-08-26 15:41:32.203 [Thread-8] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:41:32.204 [Thread-8] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：3,等待任务: 0
2025-08-26 15:41:32.204 [async-task-pool66] INFO  c.r.f.a.e.PullHostIntrusionAttackEvent - [lambda$1,83] - 开始获取主机入侵攻击数据: 测试非凡设备1
2025-08-26 15:41:32.204 [async-task-pool47] INFO  c.r.f.a.e.PullHostIntrusionAttackEvent - [lambda$1,83] - 开始获取主机入侵攻击数据: 测试设备2
2025-08-26 15:41:32.205 [async-task-pool66] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,874] - 开始拉取主机入侵攻击数据，参数：HostIntrusionAttackParam(accessToken=null, sip=null, dip=null, startDate=2025-08-25 15:39:22, endDate=2025-08-26 15:39:32, page=1, pSize=20, deviceConfigId=1)
2025-08-26 15:41:32.205 [async-task-pool47] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,874] - 开始拉取主机入侵攻击数据，参数：HostIntrusionAttackParam(accessToken=null, sip=null, dip=null, startDate=2025-08-25 15:39:22, endDate=2025-08-26 15:39:32, page=1, pSize=20, deviceConfigId=4)
2025-08-26 15:41:32.205 [async-task-pool66] INFO  c.r.f.a.d.HostIntrusionAttackParam - [getRequestBase,80] - 非凡API主机入侵攻击分页请求参数: https://***************:23000/v2/host-edr-intrusion?access_token=lst2BAzxJTQL0eSZ&start_date=2025-08-25%2015%3A39%3A22&end_date=2025-08-26%2015%3A39%3A32&page=1&p_size=20
2025-08-26 15:41:32.206 [async-task-pool47] INFO  c.r.f.a.d.HostIntrusionAttackParam - [getRequestBase,80] - 非凡API主机入侵攻击分页请求参数: https://***************:23000/v2/host-edr-intrusion?access_token=lst2BAzxJTQL0eSZ&start_date=2025-08-25%2015%3A39%3A22&end_date=2025-08-26%2015%3A39%3A32&page=1&p_size=20
2025-08-26 15:41:32.222 [taskScheduler-39] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:41:32.223 [taskScheduler-39] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：5,等待任务: 0
2025-08-26 15:41:32.261 [async-task-pool66] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,987] - 主机入侵攻击总数: 0
2025-08-26 15:41:32.262 [async-task-pool66] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1010] - 第1页获取0条数据
2025-08-26 15:41:32.262 [async-task-pool66] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1014] - 当前页无数据，停止获取
2025-08-26 15:41:32.262 [async-task-pool66] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1083] - 拉取主机入侵攻击数据成功，但没有有效数据
2025-08-26 15:41:32.270 [async-task-pool47] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,987] - 主机入侵攻击总数: 0
2025-08-26 15:41:32.270 [async-task-pool47] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1010] - 第1页获取0条数据
2025-08-26 15:41:32.271 [async-task-pool47] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1014] - 当前页无数据，停止获取
2025-08-26 15:41:32.271 [async-task-pool47] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1083] - 拉取主机入侵攻击数据成功，但没有有效数据
2025-08-26 15:41:32.275 [async-task-pool64] INFO  c.r.f.c.FFSafeRequestComponent - [execRequest,88] - 非凡请求响应: [{"id": 1058, "block_ip": "*************", "location": ["美国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-22 00:58:57", "release_time": "2025-08-29 01:00:59", "remarks": ""}, {"id": 1057, "block_ip": "************", "location": ["法国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-21 13:55:52", "release_time": "2025-08-28 13:56:53", "remarks": ""}, {"id": 1056, "block_ip": "***********", "location": ["印度"], "action": "阻断", "conditions": ["外网恶意攻击"], "type": "流量检测", "create_time": "2025-08-20 12:44:55", "release_time": "2025-08-27 12:46:57", "remarks": ""}]

2025-08-26 15:41:32.277 [async-task-pool39] INFO  c.r.f.c.FFSafeRequestComponent - [execRequest,88] - 非凡请求响应: [{"id": 1058, "block_ip": "*************", "location": ["美国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-22 00:58:57", "release_time": "2025-08-29 01:00:59", "remarks": ""}, {"id": 1057, "block_ip": "************", "location": ["法国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-21 13:55:52", "release_time": "2025-08-28 13:56:53", "remarks": ""}, {"id": 1056, "block_ip": "***********", "location": ["印度"], "action": "阻断", "conditions": ["外网恶意攻击"], "type": "流量检测", "create_time": "2025-08-20 12:44:55", "release_time": "2025-08-27 12:46:57", "remarks": ""}]

2025-08-26 15:41:32.279 [Thread-8] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 76毫秒
2025-08-26 15:41:32.294 [taskScheduler-39] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 72毫秒
2025-08-26 15:41:32.295 [Thread-6] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:41:32.295 [Thread-6] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：3,等待任务: 0
2025-08-26 15:41:32.295 [async-task-pool62] INFO  c.r.f.a.e.PullFilterLogEvent - [lambda$1,90] - 开始获取IP阻断日志: 测试设备2
2025-08-26 15:41:32.295 [async-task-pool25] INFO  c.r.f.a.e.PullFilterLogEvent - [lambda$1,90] - 开始获取IP阻断日志: 测试非凡设备1
2025-08-26 15:41:32.329 [Thread-7] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:41:32.339 [Thread-7] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：5,等待任务: 0
2025-08-26 15:41:32.343 [async-task-pool67] INFO  c.r.f.a.e.PullFlowRiskAssetsEvent - [lambda$1,100] - 开始获取流量风险资产数据: 测试非凡设备1
2025-08-26 15:41:32.343 [async-task-pool69] INFO  c.r.f.a.e.PullFlowRiskAssetsEvent - [lambda$1,100] - 开始获取流量风险资产数据: 测试设备2
2025-08-26 15:41:32.345 [async-task-pool67] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,352] - 开始拉取流量风险资产数据，参数：FlowRiskAssetsParam(accessToken=null, riskType=all, startTime=2025-08-25 15:39:28, endTime=2025-08-26 15:39:32, page=1, pageSize=30, deviceConfigId=1)
2025-08-26 15:41:32.345 [async-task-pool69] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,352] - 开始拉取流量风险资产数据，参数：FlowRiskAssetsParam(accessToken=null, riskType=all, startTime=2025-08-25 15:39:28, endTime=2025-08-26 15:39:32, page=1, pageSize=30, deviceConfigId=4)
2025-08-26 15:41:32.347 [async-task-pool67] INFO  c.r.f.a.d.FlowRiskAssetsParam - [getRequestBase,49] - 非凡API流量风险资产请求参数: https://***************:23000/v2/flow-risk-assets?access_token=lst2BAzxJTQL0eSZ&risk_type=all&start_date=2025-08-25%2015%3A39%3A28&end_date=2025-08-26%2015%3A39%3A32&page=1&page_size=30
2025-08-26 15:41:32.352 [async-task-pool69] INFO  c.r.f.a.d.FlowRiskAssetsParam - [getRequestBase,49] - 非凡API流量风险资产请求参数: https://***************:23000/v2/flow-risk-assets?access_token=lst2BAzxJTQL0eSZ&risk_type=all&start_date=2025-08-25%2015%3A39%3A28&end_date=2025-08-26%2015%3A39%3A32&page=1&page_size=30
2025-08-26 15:41:32.425 [Thread-6] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 130毫秒
2025-08-26 15:41:32.459 [async-task-pool67] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,458] - 流量风险资产总数: 6
2025-08-26 15:41:32.460 [async-task-pool67] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,481] - 第1页获取6条数据
2025-08-26 15:41:32.461 [async-task-pool69] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,458] - 流量风险资产总数: 6
2025-08-26 15:41:32.461 [async-task-pool67] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,488] - 当前页数据量(6)少于页大小(30)，已获取完所有数据
2025-08-26 15:41:32.463 [async-task-pool69] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,481] - 第1页获取6条数据
2025-08-26 15:41:32.478 [async-task-pool69] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,488] - 当前页数据量(6)少于页大小(30)，已获取完所有数据
2025-08-26 15:41:32.643 [async-task-pool67] INFO  c.r.m.c.c.FfsafeClientService - [processBatch,662] - 批量更新6条流量风险资产数据
2025-08-26 15:41:32.644 [async-task-pool69] INFO  c.r.m.c.c.FfsafeClientService - [processBatch,662] - 批量更新6条流量风险资产数据
2025-08-26 15:41:32.645 [async-task-pool67] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,551] - 拉取流量风险资产数据完成，耗时300ms，共1页，共6条数据，处理结果: true
2025-08-26 15:41:32.645 [async-task-pool69] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,551] - 拉取流量风险资产数据完成，耗时300ms，共1页，共6条数据，处理结果: true
2025-08-26 15:41:32.678 [Thread-7] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 349毫秒
2025-08-26 15:41:34.533 [taskScheduler-38] INFO  c.r.r.s.HandleDataSyncSender - [sendDataSync,230] - 数据同步发送成功, messageId: f6cbd556-06bc-4339-a889-d970fc23781c, 加密状态: 已加密, 消息类型: PING
2025-08-26 15:41:34.538 [taskScheduler-38] INFO  c.r.r.t.RabbitMQTask - [ping,44] - 发送心跳消息
2025-08-26 15:41:34.538 [rabbitConnectionFactory10] INFO  c.r.f.c.r.RabbitMQConfig - [lambda$0,290] - 消息已成功发送到交换机, messageId: f6cbd556-06bc-4339-a889-d970fc23781c
2025-08-26 15:41:35.102 [taskScheduler-44] INFO  c.r.c.task.HlsTask - [checkHls,65] - hls检查开始...
2025-08-26 15:41:35.104 [taskScheduler-44] INFO  c.r.c.task.HlsTask - [checkHls,86] - hls检查结束...
2025-08-26 15:41:40.517 [Thread-5] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:41:40.518 [Thread-5] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：2,等待任务: 0
2025-08-26 15:41:40.518 [async-task-pool61] INFO  c.r.f.a.e.PullDataEvent - [lambda$1,102] - 开始流量告警日志同步: 测试非凡设备1
2025-08-26 15:41:40.518 [async-task-pool65] INFO  c.r.f.a.e.PullDataEvent - [lambda$1,102] - 开始流量告警日志同步: 测试设备2
2025-08-26 15:41:40.567 [pool-4-thread-1] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [lambda$0,81] - 最大连接：200 当前连接: 3
2025-08-26 15:41:41.154 [Thread-5] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 637毫秒
2025-08-26 15:41:42.298 [Thread-8] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:41:42.298 [Thread-8] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：2,等待任务: 0
2025-08-26 15:41:42.298 [async-task-pool181] INFO  c.r.f.a.e.PullHostIntrusionAttackEvent - [lambda$1,83] - 开始获取主机入侵攻击数据: 测试非凡设备1
2025-08-26 15:41:42.298 [async-task-pool58] INFO  c.r.f.a.e.PullHostIntrusionAttackEvent - [lambda$1,83] - 开始获取主机入侵攻击数据: 测试设备2
2025-08-26 15:41:42.299 [async-task-pool181] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,874] - 开始拉取主机入侵攻击数据，参数：HostIntrusionAttackParam(accessToken=null, sip=null, dip=null, startDate=2025-08-25 15:39:32, endDate=2025-08-26 15:39:42, page=1, pSize=20, deviceConfigId=1)
2025-08-26 15:41:42.299 [async-task-pool58] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,874] - 开始拉取主机入侵攻击数据，参数：HostIntrusionAttackParam(accessToken=null, sip=null, dip=null, startDate=2025-08-25 15:39:32, endDate=2025-08-26 15:39:42, page=1, pSize=20, deviceConfigId=4)
2025-08-26 15:41:42.301 [async-task-pool181] INFO  c.r.f.a.d.HostIntrusionAttackParam - [getRequestBase,80] - 非凡API主机入侵攻击分页请求参数: https://***************:23000/v2/host-edr-intrusion?access_token=lst2BAzxJTQL0eSZ&start_date=2025-08-25%2015%3A39%3A32&end_date=2025-08-26%2015%3A39%3A42&page=1&p_size=20
2025-08-26 15:41:42.301 [taskScheduler-13] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:41:42.301 [async-task-pool58] INFO  c.r.f.a.d.HostIntrusionAttackParam - [getRequestBase,80] - 非凡API主机入侵攻击分页请求参数: https://***************:23000/v2/host-edr-intrusion?access_token=lst2BAzxJTQL0eSZ&start_date=2025-08-25%2015%3A39%3A32&end_date=2025-08-26%2015%3A39%3A42&page=1&p_size=20
2025-08-26 15:41:42.303 [taskScheduler-13] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：4,等待任务: 0
2025-08-26 15:41:42.401 [async-task-pool68] INFO  c.r.f.c.FFSafeRequestComponent - [execRequest,88] - 非凡请求响应: [{"id": 1058, "block_ip": "*************", "location": ["美国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-22 00:58:57", "release_time": "2025-08-29 01:00:59", "remarks": ""}, {"id": 1057, "block_ip": "************", "location": ["法国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-21 13:55:52", "release_time": "2025-08-28 13:56:53", "remarks": ""}, {"id": 1056, "block_ip": "***********", "location": ["印度"], "action": "阻断", "conditions": ["外网恶意攻击"], "type": "流量检测", "create_time": "2025-08-20 12:44:55", "release_time": "2025-08-27 12:46:57", "remarks": ""}]

2025-08-26 15:41:42.405 [async-task-pool54] INFO  c.r.f.c.FFSafeRequestComponent - [execRequest,88] - 非凡请求响应: [{"id": 1058, "block_ip": "*************", "location": ["美国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-22 00:58:57", "release_time": "2025-08-29 01:00:59", "remarks": ""}, {"id": 1057, "block_ip": "************", "location": ["法国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-21 13:55:52", "release_time": "2025-08-28 13:56:53", "remarks": ""}, {"id": 1056, "block_ip": "***********", "location": ["印度"], "action": "阻断", "conditions": ["外网恶意攻击"], "type": "流量检测", "create_time": "2025-08-20 12:44:55", "release_time": "2025-08-27 12:46:57", "remarks": ""}]

2025-08-26 15:41:42.417 [async-task-pool181] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,987] - 主机入侵攻击总数: 0
2025-08-26 15:41:42.418 [async-task-pool181] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1010] - 第1页获取0条数据
2025-08-26 15:41:42.420 [async-task-pool181] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1014] - 当前页无数据，停止获取
2025-08-26 15:41:42.421 [async-task-pool181] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1083] - 拉取主机入侵攻击数据成功，但没有有效数据
2025-08-26 15:41:42.423 [async-task-pool58] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,987] - 主机入侵攻击总数: 0
2025-08-26 15:41:42.430 [async-task-pool58] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1010] - 第1页获取0条数据
2025-08-26 15:41:42.436 [async-task-pool58] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1014] - 当前页无数据，停止获取
2025-08-26 15:41:42.437 [async-task-pool58] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1083] - 拉取主机入侵攻击数据成功，但没有有效数据
2025-08-26 15:41:42.450 [taskScheduler-13] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 149毫秒
2025-08-26 15:41:42.450 [Thread-8] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 152毫秒
2025-08-26 15:41:42.450 [Thread-6] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:41:42.453 [Thread-6] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：2,等待任务: 0
2025-08-26 15:41:42.453 [async-task-pool57] INFO  c.r.f.a.e.PullFilterLogEvent - [lambda$1,90] - 开始获取IP阻断日志: 测试非凡设备1
2025-08-26 15:41:42.453 [async-task-pool46] INFO  c.r.f.a.e.PullFilterLogEvent - [lambda$1,90] - 开始获取IP阻断日志: 测试设备2
2025-08-26 15:41:42.585 [Thread-6] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 135毫秒
2025-08-26 15:41:42.742 [Thread-7] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:41:42.745 [Thread-7] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：2,等待任务: 0
2025-08-26 15:41:42.752 [async-task-pool45] INFO  c.r.f.a.e.PullFlowRiskAssetsEvent - [lambda$1,100] - 开始获取流量风险资产数据: 测试设备2
2025-08-26 15:41:42.752 [async-task-pool73] INFO  c.r.f.a.e.PullFlowRiskAssetsEvent - [lambda$1,100] - 开始获取流量风险资产数据: 测试非凡设备1
2025-08-26 15:41:42.753 [async-task-pool45] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,352] - 开始拉取流量风险资产数据，参数：FlowRiskAssetsParam(accessToken=null, riskType=all, startTime=2025-08-25 15:39:38, endTime=2025-08-26 15:39:42, page=1, pageSize=30, deviceConfigId=4)
2025-08-26 15:41:42.753 [async-task-pool73] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,352] - 开始拉取流量风险资产数据，参数：FlowRiskAssetsParam(accessToken=null, riskType=all, startTime=2025-08-25 15:39:38, endTime=2025-08-26 15:39:42, page=1, pageSize=30, deviceConfigId=1)
2025-08-26 15:41:42.754 [async-task-pool45] INFO  c.r.f.a.d.FlowRiskAssetsParam - [getRequestBase,49] - 非凡API流量风险资产请求参数: https://***************:23000/v2/flow-risk-assets?access_token=lst2BAzxJTQL0eSZ&risk_type=all&start_date=2025-08-25%2015%3A39%3A38&end_date=2025-08-26%2015%3A39%3A42&page=1&page_size=30
2025-08-26 15:41:42.755 [async-task-pool73] INFO  c.r.f.a.d.FlowRiskAssetsParam - [getRequestBase,49] - 非凡API流量风险资产请求参数: https://***************:23000/v2/flow-risk-assets?access_token=lst2BAzxJTQL0eSZ&risk_type=all&start_date=2025-08-25%2015%3A39%3A38&end_date=2025-08-26%2015%3A39%3A42&page=1&page_size=30
2025-08-26 15:41:42.926 [async-task-pool73] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,458] - 流量风险资产总数: 6
2025-08-26 15:41:42.926 [async-task-pool73] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,481] - 第1页获取6条数据
2025-08-26 15:41:42.928 [async-task-pool73] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,488] - 当前页数据量(6)少于页大小(30)，已获取完所有数据
2025-08-26 15:41:42.940 [async-task-pool45] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,458] - 流量风险资产总数: 6
2025-08-26 15:41:42.955 [async-task-pool45] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,481] - 第1页获取6条数据
2025-08-26 15:41:42.957 [async-task-pool45] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,488] - 当前页数据量(6)少于页大小(30)，已获取完所有数据
2025-08-26 15:41:43.028 [async-task-pool73] INFO  c.r.m.c.c.FfsafeClientService - [processBatch,662] - 批量更新6条流量风险资产数据
2025-08-26 15:41:43.031 [async-task-pool73] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,551] - 拉取流量风险资产数据完成，耗时278ms，共1页，共6条数据，处理结果: true
2025-08-26 15:41:43.062 [async-task-pool45] INFO  c.r.m.c.c.FfsafeClientService - [processBatch,662] - 批量更新6条流量风险资产数据
2025-08-26 15:41:43.068 [async-task-pool45] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,551] - 拉取流量风险资产数据完成，耗时315ms，共1页，共6条数据，处理结果: true
2025-08-26 15:41:43.113 [Thread-7] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 371毫秒
2025-08-26 15:41:49.923 [pool-6-thread-4] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 20秒932毫秒
2025-08-26 15:41:50.586 [pool-4-thread-1] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [lambda$0,81] - 最大连接：200 当前连接: 1
2025-08-26 15:41:51.191 [Thread-5] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:41:51.191 [Thread-5] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：0,等待任务: 0
2025-08-26 15:41:51.192 [async-task-pool10] INFO  c.r.f.a.e.PullDataEvent - [lambda$1,102] - 开始流量告警日志同步: 测试设备2
2025-08-26 15:41:51.192 [async-task-pool33] INFO  c.r.f.a.e.PullDataEvent - [lambda$1,102] - 开始流量告警日志同步: 测试非凡设备1
2025-08-26 15:41:51.303 [pool-6-thread-4] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sync,255] - 批量更新攻击者视角告警列开始: 12
2025-08-26 15:41:51.377 [pool-6-thread-4] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=**************, operationType=UPDATE
2025-08-26 15:41:51.378 [Thread-5] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 187毫秒
2025-08-26 15:41:51.379 [pool-6-thread-4] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=**************, operationType=UPDATE
2025-08-26 15:41:51.388 [pool-6-thread-4] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=**************, operationType=UPDATE
2025-08-26 15:41:51.390 [pool-6-thread-4] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=**********0, operationType=UPDATE
2025-08-26 15:41:51.392 [pool-6-thread-4] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=*************, operationType=UPDATE
2025-08-26 15:41:51.393 [pool-6-thread-4] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=**************, operationType=UPDATE
2025-08-26 15:41:51.394 [pool-6-thread-4] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=*************, operationType=UPDATE
2025-08-26 15:41:51.395 [pool-6-thread-4] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=************, operationType=UPDATE
2025-08-26 15:41:51.397 [pool-6-thread-4] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=***********, operationType=UPDATE
2025-08-26 15:41:51.404 [pool-6-thread-4] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=***************, operationType=UPDATE
2025-08-26 15:41:51.406 [pool-6-thread-4] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=***********, operationType=UPDATE
2025-08-26 15:41:51.408 [pool-6-thread-4] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=**************, operationType=UPDATE
2025-08-26 15:41:51.642 [pool-6-thread-1] INFO  c.r.s.t.FfSafeFlowAlarmTask - [syncTask,46] - 开始同步攻击者告警数据
2025-08-26 15:41:51.835 [pool-6-thread-1] INFO  c.r.f.c.FFSafeRequestComponent - [execRequest,88] - 非凡请求响应: [{"attack_ip": "**************", "risk_level": 2, "ip_tags": ["弱口令登录成功"], "location": "局域网", "victim_ip_nums": 2, "attack_type_nums": 5, "attack_nums": 7380, "start_time": "2025-08-25 08:00:02", "update_time": "2025-08-26 15:41:10"}, {"attack_ip": "**********0", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 1, "attack_type_nums": 1, "attack_nums": 585, "start_time": "2025-08-25 08:59:16", "update_time": "2025-08-26 15:40:01"}, {"attack_ip": "*************", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 2, "attack_type_nums": 1, "attack_nums": 333, "start_time": "2025-08-25 08:03:53", "update_time": "2025-08-26 15:39:58"}, {"attack_ip": "**************", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 1, "attack_type_nums": 1, "attack_nums": 14, "start_time": "2025-08-26 11:06:34", "update_time": "2025-08-26 15:39:35"}, {"attack_ip": "*************", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 2, "attack_type_nums": 1, "attack_nums": 336, "start_time": "2025-08-25 08:02:29", "update_time": "2025-08-26 15:38:37"}, {"attack_ip": "************", "risk_level": 3, "ip_tags": ["内网恶意攻击"], "location": "局域网", "victim_ip_nums": 5, "attack_type_nums": 6, "attack_nums": 97, "start_time": "2025-08-25 16:17:50", "update_time": "2025-08-26 15:37:44"}, {"attack_ip": "***********", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 1, "attack_type_nums": 1, "attack_nums": 239, "start_time": "2025-08-26 09:54:16", "update_time": "2025-08-26 15:37:42"}, {"attack_ip": "***************", "risk_level": 3, "ip_tags": ["内网端口扫描", "内网恶意攻击", "暴力破解"], "location": "局域网", "victim_ip_nums": 30, "attack_type_nums": 1079, "attack_nums": 58440, "start_time": "2025-08-26 14:58:22", "update_time": "2025-08-26 15:34:10"}, {"attack_ip": "***********", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 2, "attack_type_nums": 2, "attack_nums": 155, "start_time": "2025-08-25 13:27:18", "update_time": "2025-08-26 15:33:16"}, {"attack_ip": "**************", "risk_level": 3, "ip_tags": ["内网端口扫描", "内网恶意攻击", "暴力破解"], "location": "局域网", "victim_ip_nums": 257, "attack_type_nums": 62, "attack_nums": 1176, "start_time": "2025-08-26 14:24:32", "update_time": "2025-08-26 15:32:50"}, {"attack_ip": "***********", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 1, "attack_type_nums": 3, "attack_nums": 106, "start_time": "2025-08-26 09:49:05", "update_time": "2025-08-26 15:31:50"}]

2025-08-26 15:41:51.845 [pool-6-thread-1] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:11...
2025-08-26 15:41:51.846 [pool-6-thread-1] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：0,等待任务: 0
2025-08-26 15:41:52.458 [taskScheduler-13] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:41:52.459 [taskScheduler-13] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：2,等待任务: 0
2025-08-26 15:41:52.473 [Thread-8] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:41:52.474 [Thread-8] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：4,等待任务: 0
2025-08-26 15:41:52.474 [async-task-pool95] INFO  c.r.f.a.e.PullHostIntrusionAttackEvent - [lambda$1,83] - 开始获取主机入侵攻击数据: 测试非凡设备1
2025-08-26 15:41:52.474 [async-task-pool76] INFO  c.r.f.a.e.PullHostIntrusionAttackEvent - [lambda$1,83] - 开始获取主机入侵攻击数据: 测试设备2
2025-08-26 15:41:52.474 [async-task-pool95] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,874] - 开始拉取主机入侵攻击数据，参数：HostIntrusionAttackParam(accessToken=null, sip=null, dip=null, startDate=2025-08-25 15:39:42, endDate=2025-08-26 15:39:52, page=1, pSize=20, deviceConfigId=1)
2025-08-26 15:41:52.475 [async-task-pool76] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,874] - 开始拉取主机入侵攻击数据，参数：HostIntrusionAttackParam(accessToken=null, sip=null, dip=null, startDate=2025-08-25 15:39:42, endDate=2025-08-26 15:39:52, page=1, pSize=20, deviceConfigId=4)
2025-08-26 15:41:52.476 [async-task-pool95] INFO  c.r.f.a.d.HostIntrusionAttackParam - [getRequestBase,80] - 非凡API主机入侵攻击分页请求参数: https://***************:23000/v2/host-edr-intrusion?access_token=lst2BAzxJTQL0eSZ&start_date=2025-08-25%2015%3A39%3A42&end_date=2025-08-26%2015%3A39%3A52&page=1&p_size=20
2025-08-26 15:41:52.476 [async-task-pool76] INFO  c.r.f.a.d.HostIntrusionAttackParam - [getRequestBase,80] - 非凡API主机入侵攻击分页请求参数: https://***************:23000/v2/host-edr-intrusion?access_token=lst2BAzxJTQL0eSZ&start_date=2025-08-25%2015%3A39%3A42&end_date=2025-08-26%2015%3A39%3A52&page=1&p_size=20
2025-08-26 15:41:52.506 [async-task-pool86] INFO  c.r.f.c.FFSafeRequestComponent - [execRequest,88] - 非凡请求响应: [{"id": 1058, "block_ip": "*************", "location": ["美国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-22 00:58:57", "release_time": "2025-08-29 01:00:59", "remarks": ""}, {"id": 1057, "block_ip": "************", "location": ["法国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-21 13:55:52", "release_time": "2025-08-28 13:56:53", "remarks": ""}, {"id": 1056, "block_ip": "***********", "location": ["印度"], "action": "阻断", "conditions": ["外网恶意攻击"], "type": "流量检测", "create_time": "2025-08-20 12:44:55", "release_time": "2025-08-27 12:46:57", "remarks": ""}]

2025-08-26 15:41:52.528 [async-task-pool80] INFO  c.r.f.c.FFSafeRequestComponent - [execRequest,88] - 非凡请求响应: [{"id": 1058, "block_ip": "*************", "location": ["美国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-22 00:58:57", "release_time": "2025-08-29 01:00:59", "remarks": ""}, {"id": 1057, "block_ip": "************", "location": ["法国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-21 13:55:52", "release_time": "2025-08-28 13:56:53", "remarks": ""}, {"id": 1056, "block_ip": "***********", "location": ["印度"], "action": "阻断", "conditions": ["外网恶意攻击"], "type": "流量检测", "create_time": "2025-08-20 12:44:55", "release_time": "2025-08-27 12:46:57", "remarks": ""}]

2025-08-26 15:41:52.542 [async-task-pool95] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,987] - 主机入侵攻击总数: 0
2025-08-26 15:41:52.542 [async-task-pool95] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1010] - 第1页获取0条数据
2025-08-26 15:41:52.544 [async-task-pool95] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1014] - 当前页无数据，停止获取
2025-08-26 15:41:52.546 [async-task-pool95] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1083] - 拉取主机入侵攻击数据成功，但没有有效数据
2025-08-26 15:41:52.555 [async-task-pool76] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,987] - 主机入侵攻击总数: 0
2025-08-26 15:41:52.555 [async-task-pool76] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1010] - 第1页获取0条数据
2025-08-26 15:41:52.556 [async-task-pool76] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1014] - 当前页无数据，停止获取
2025-08-26 15:41:52.557 [async-task-pool76] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1083] - 拉取主机入侵攻击数据成功，但没有有效数据
2025-08-26 15:41:52.562 [taskScheduler-13] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 104毫秒
2025-08-26 15:41:52.575 [Thread-8] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 102毫秒
2025-08-26 15:41:52.592 [Thread-6] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:41:52.593 [Thread-6] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：2,等待任务: 0
2025-08-26 15:41:52.593 [async-task-pool31] INFO  c.r.f.a.e.PullFilterLogEvent - [lambda$1,90] - 开始获取IP阻断日志: 测试设备2
2025-08-26 15:41:52.593 [async-task-pool19] INFO  c.r.f.a.e.PullFilterLogEvent - [lambda$1,90] - 开始获取IP阻断日志: 测试非凡设备1
2025-08-26 15:41:52.653 [Thread-6] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 61毫秒
2025-08-26 15:41:53.121 [Thread-7] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:41:53.122 [Thread-7] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：2,等待任务: 0
2025-08-26 15:41:53.122 [async-task-pool105] INFO  c.r.f.a.e.PullFlowRiskAssetsEvent - [lambda$1,100] - 开始获取流量风险资产数据: 测试非凡设备1
2025-08-26 15:41:53.122 [async-task-pool91] INFO  c.r.f.a.e.PullFlowRiskAssetsEvent - [lambda$1,100] - 开始获取流量风险资产数据: 测试设备2
2025-08-26 15:41:53.122 [async-task-pool105] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,352] - 开始拉取流量风险资产数据，参数：FlowRiskAssetsParam(accessToken=null, riskType=all, startTime=2025-08-25 15:39:48, endTime=2025-08-26 15:39:53, page=1, pageSize=30, deviceConfigId=1)
2025-08-26 15:41:53.123 [async-task-pool91] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,352] - 开始拉取流量风险资产数据，参数：FlowRiskAssetsParam(accessToken=null, riskType=all, startTime=2025-08-25 15:39:48, endTime=2025-08-26 15:39:53, page=1, pageSize=30, deviceConfigId=4)
2025-08-26 15:41:53.124 [async-task-pool105] INFO  c.r.f.a.d.FlowRiskAssetsParam - [getRequestBase,49] - 非凡API流量风险资产请求参数: https://***************:23000/v2/flow-risk-assets?access_token=lst2BAzxJTQL0eSZ&risk_type=all&start_date=2025-08-25%2015%3A39%3A48&end_date=2025-08-26%2015%3A39%3A53&page=1&page_size=30
2025-08-26 15:41:53.125 [async-task-pool91] INFO  c.r.f.a.d.FlowRiskAssetsParam - [getRequestBase,49] - 非凡API流量风险资产请求参数: https://***************:23000/v2/flow-risk-assets?access_token=lst2BAzxJTQL0eSZ&risk_type=all&start_date=2025-08-25%2015%3A39%3A48&end_date=2025-08-26%2015%3A39%3A53&page=1&page_size=30
2025-08-26 15:41:53.194 [async-task-pool105] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,458] - 流量风险资产总数: 6
2025-08-26 15:41:53.195 [async-task-pool105] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,481] - 第1页获取6条数据
2025-08-26 15:41:53.195 [async-task-pool105] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,488] - 当前页数据量(6)少于页大小(30)，已获取完所有数据
2025-08-26 15:41:53.197 [async-task-pool91] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,458] - 流量风险资产总数: 6
2025-08-26 15:41:53.202 [async-task-pool91] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,481] - 第1页获取6条数据
2025-08-26 15:41:53.203 [async-task-pool91] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,488] - 当前页数据量(6)少于页大小(30)，已获取完所有数据
2025-08-26 15:41:53.221 [async-task-pool105] INFO  c.r.m.c.c.FfsafeClientService - [processBatch,662] - 批量更新6条流量风险资产数据
2025-08-26 15:41:53.225 [async-task-pool105] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,551] - 拉取流量风险资产数据完成，耗时103ms，共1页，共6条数据，处理结果: true
2025-08-26 15:41:53.229 [async-task-pool91] INFO  c.r.m.c.c.FfsafeClientService - [processBatch,662] - 批量更新6条流量风险资产数据
2025-08-26 15:41:53.230 [async-task-pool91] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,551] - 拉取流量风险资产数据完成，耗时107ms，共1页，共6条数据，处理结果: true
2025-08-26 15:41:53.239 [Thread-7] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 118毫秒
2025-08-26 15:42:00.588 [pool-4-thread-1] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [lambda$0,81] - 最大连接：200 当前连接: 2
2025-08-26 15:42:01.388 [Thread-5] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:42:01.388 [Thread-5] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：1,等待任务: 0
2025-08-26 15:42:01.388 [async-task-pool85] INFO  c.r.f.a.e.PullDataEvent - [lambda$1,102] - 开始流量告警日志同步: 测试设备2
2025-08-26 15:42:01.388 [async-task-pool89] INFO  c.r.f.a.e.PullDataEvent - [lambda$1,102] - 开始流量告警日志同步: 测试非凡设备1
2025-08-26 15:42:02.048 [Thread-5] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 660毫秒
2025-08-26 15:42:02.569 [taskScheduler-13] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:42:02.570 [taskScheduler-13] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：1,等待任务: 0
2025-08-26 15:42:02.582 [Thread-8] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:42:02.583 [Thread-8] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：3,等待任务: 0
2025-08-26 15:42:02.583 [async-task-pool51] INFO  c.r.f.a.e.PullHostIntrusionAttackEvent - [lambda$1,83] - 开始获取主机入侵攻击数据: 测试设备2
2025-08-26 15:42:02.583 [async-task-pool92] INFO  c.r.f.a.e.PullHostIntrusionAttackEvent - [lambda$1,83] - 开始获取主机入侵攻击数据: 测试非凡设备1
2025-08-26 15:42:02.583 [async-task-pool51] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,874] - 开始拉取主机入侵攻击数据，参数：HostIntrusionAttackParam(accessToken=null, sip=null, dip=null, startDate=2025-08-25 15:39:52, endDate=2025-08-26 15:40:02, page=1, pSize=20, deviceConfigId=4)
2025-08-26 15:42:02.583 [async-task-pool92] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,874] - 开始拉取主机入侵攻击数据，参数：HostIntrusionAttackParam(accessToken=null, sip=null, dip=null, startDate=2025-08-25 15:39:52, endDate=2025-08-26 15:40:02, page=1, pSize=20, deviceConfigId=1)
2025-08-26 15:42:02.584 [async-task-pool51] INFO  c.r.f.a.d.HostIntrusionAttackParam - [getRequestBase,80] - 非凡API主机入侵攻击分页请求参数: https://***************:23000/v2/host-edr-intrusion?access_token=lst2BAzxJTQL0eSZ&start_date=2025-08-25%2015%3A39%3A52&end_date=2025-08-26%2015%3A40%3A02&page=1&p_size=20
2025-08-26 15:42:02.585 [async-task-pool92] INFO  c.r.f.a.d.HostIntrusionAttackParam - [getRequestBase,80] - 非凡API主机入侵攻击分页请求参数: https://***************:23000/v2/host-edr-intrusion?access_token=lst2BAzxJTQL0eSZ&start_date=2025-08-25%2015%3A39%3A52&end_date=2025-08-26%2015%3A40%3A02&page=1&p_size=20
2025-08-26 15:42:02.654 [async-task-pool77] INFO  c.r.f.c.FFSafeRequestComponent - [execRequest,88] - 非凡请求响应: [{"id": 1058, "block_ip": "*************", "location": ["美国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-22 00:58:57", "release_time": "2025-08-29 01:00:59", "remarks": ""}, {"id": 1057, "block_ip": "************", "location": ["法国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-21 13:55:52", "release_time": "2025-08-28 13:56:53", "remarks": ""}, {"id": 1056, "block_ip": "***********", "location": ["印度"], "action": "阻断", "conditions": ["外网恶意攻击"], "type": "流量检测", "create_time": "2025-08-20 12:44:55", "release_time": "2025-08-27 12:46:57", "remarks": ""}]

2025-08-26 15:42:02.668 [async-task-pool87] INFO  c.r.f.c.FFSafeRequestComponent - [execRequest,88] - 非凡请求响应: [{"id": 1058, "block_ip": "*************", "location": ["美国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-22 00:58:57", "release_time": "2025-08-29 01:00:59", "remarks": ""}, {"id": 1057, "block_ip": "************", "location": ["法国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-21 13:55:52", "release_time": "2025-08-28 13:56:53", "remarks": ""}, {"id": 1056, "block_ip": "***********", "location": ["印度"], "action": "阻断", "conditions": ["外网恶意攻击"], "type": "流量检测", "create_time": "2025-08-20 12:44:55", "release_time": "2025-08-27 12:46:57", "remarks": ""}]

2025-08-26 15:42:02.670 [async-task-pool51] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,987] - 主机入侵攻击总数: 0
2025-08-26 15:42:02.672 [async-task-pool92] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,987] - 主机入侵攻击总数: 0
2025-08-26 15:42:02.672 [async-task-pool51] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1010] - 第1页获取0条数据
2025-08-26 15:42:02.672 [async-task-pool92] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1010] - 第1页获取0条数据
2025-08-26 15:42:02.673 [async-task-pool51] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1014] - 当前页无数据，停止获取
2025-08-26 15:42:02.675 [async-task-pool92] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1014] - 当前页无数据，停止获取
2025-08-26 15:42:02.676 [Thread-6] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:42:02.676 [async-task-pool51] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1083] - 拉取主机入侵攻击数据成功，但没有有效数据
2025-08-26 15:42:02.677 [async-task-pool92] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1083] - 拉取主机入侵攻击数据成功，但没有有效数据
2025-08-26 15:42:02.677 [Thread-6] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：5,等待任务: 0
2025-08-26 15:42:02.680 [async-task-pool60] INFO  c.r.f.a.e.PullFilterLogEvent - [lambda$1,90] - 开始获取IP阻断日志: 测试非凡设备1
2025-08-26 15:42:02.680 [async-task-pool50] INFO  c.r.f.a.e.PullFilterLogEvent - [lambda$1,90] - 开始获取IP阻断日志: 测试设备2
2025-08-26 15:42:02.696 [taskScheduler-13] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 127毫秒
2025-08-26 15:42:02.705 [Thread-8] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 123毫秒
2025-08-26 15:42:02.754 [Thread-6] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 78毫秒
2025-08-26 15:42:03.248 [Thread-7] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:42:03.248 [Thread-7] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：1,等待任务: 0
2025-08-26 15:42:03.248 [async-task-pool74] INFO  c.r.f.a.e.PullFlowRiskAssetsEvent - [lambda$1,100] - 开始获取流量风险资产数据: 测试非凡设备1
2025-08-26 15:42:03.248 [async-task-pool107] INFO  c.r.f.a.e.PullFlowRiskAssetsEvent - [lambda$1,100] - 开始获取流量风险资产数据: 测试设备2
2025-08-26 15:42:03.249 [async-task-pool74] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,352] - 开始拉取流量风险资产数据，参数：FlowRiskAssetsParam(accessToken=null, riskType=all, startTime=2025-08-25 15:39:59, endTime=2025-08-26 15:40:03, page=1, pageSize=30, deviceConfigId=1)
2025-08-26 15:42:03.249 [async-task-pool107] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,352] - 开始拉取流量风险资产数据，参数：FlowRiskAssetsParam(accessToken=null, riskType=all, startTime=2025-08-25 15:39:59, endTime=2025-08-26 15:40:03, page=1, pageSize=30, deviceConfigId=4)
2025-08-26 15:42:03.250 [async-task-pool74] INFO  c.r.f.a.d.FlowRiskAssetsParam - [getRequestBase,49] - 非凡API流量风险资产请求参数: https://***************:23000/v2/flow-risk-assets?access_token=lst2BAzxJTQL0eSZ&risk_type=all&start_date=2025-08-25%2015%3A39%3A59&end_date=2025-08-26%2015%3A40%3A03&page=1&page_size=30
2025-08-26 15:42:03.251 [async-task-pool107] INFO  c.r.f.a.d.FlowRiskAssetsParam - [getRequestBase,49] - 非凡API流量风险资产请求参数: https://***************:23000/v2/flow-risk-assets?access_token=lst2BAzxJTQL0eSZ&risk_type=all&start_date=2025-08-25%2015%3A39%3A59&end_date=2025-08-26%2015%3A40%3A03&page=1&page_size=30
2025-08-26 15:42:03.324 [async-task-pool74] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,458] - 流量风险资产总数: 6
2025-08-26 15:42:03.326 [async-task-pool74] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,481] - 第1页获取6条数据
2025-08-26 15:42:03.327 [async-task-pool74] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,488] - 当前页数据量(6)少于页大小(30)，已获取完所有数据
2025-08-26 15:42:03.327 [async-task-pool107] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,458] - 流量风险资产总数: 6
2025-08-26 15:42:03.328 [async-task-pool107] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,481] - 第1页获取6条数据
2025-08-26 15:42:03.329 [async-task-pool107] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,488] - 当前页数据量(6)少于页大小(30)，已获取完所有数据
2025-08-26 15:42:03.357 [async-task-pool74] INFO  c.r.m.c.c.FfsafeClientService - [processBatch,662] - 批量更新6条流量风险资产数据
2025-08-26 15:42:03.363 [async-task-pool74] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,551] - 拉取流量风险资产数据完成，耗时114ms，共1页，共6条数据，处理结果: true
2025-08-26 15:42:03.368 [async-task-pool107] INFO  c.r.m.c.c.FfsafeClientService - [processBatch,662] - 批量更新6条流量风险资产数据
2025-08-26 15:42:03.368 [async-task-pool107] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,551] - 拉取流量风险资产数据完成，耗时119ms，共1页，共6条数据，处理结果: true
2025-08-26 15:42:03.373 [Thread-7] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 125毫秒
2025-08-26 15:42:07.955 [pool-6-thread-1] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 16秒110毫秒
2025-08-26 15:42:08.317 [pool-6-thread-1] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sync,255] - 批量更新攻击者视角告警列开始: 11
2025-08-26 15:42:08.344 [pool-6-thread-1] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=**************, operationType=UPDATE
2025-08-26 15:42:08.349 [pool-6-thread-1] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=**********0, operationType=UPDATE
2025-08-26 15:42:08.353 [pool-6-thread-1] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=*************, operationType=UPDATE
2025-08-26 15:42:08.355 [pool-6-thread-1] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=**************, operationType=UPDATE
2025-08-26 15:42:08.357 [pool-6-thread-1] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=*************, operationType=UPDATE
2025-08-26 15:42:08.358 [pool-6-thread-1] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=************, operationType=UPDATE
2025-08-26 15:42:08.360 [pool-6-thread-1] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=***********, operationType=UPDATE
2025-08-26 15:42:08.366 [pool-6-thread-1] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=***************, operationType=UPDATE
2025-08-26 15:42:08.370 [pool-6-thread-1] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=***********, operationType=UPDATE
2025-08-26 15:42:08.374 [pool-6-thread-1] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=**************, operationType=UPDATE
2025-08-26 15:42:08.375 [pool-6-thread-1] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=***********, operationType=UPDATE
2025-08-26 15:42:08.387 [pool-6-thread-5] INFO  c.r.s.t.FfSafeFlowAlarmTask - [syncTask,46] - 开始同步攻击者告警数据
2025-08-26 15:42:08.464 [pool-6-thread-5] INFO  c.r.f.c.FFSafeRequestComponent - [execRequest,88] - 非凡请求响应: [{"attack_ip": "*************", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 2, "attack_type_nums": 1, "attack_nums": 333, "start_time": "2025-08-25 08:03:53", "update_time": "2025-08-26 15:39:58"}, {"attack_ip": "**************", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 1, "attack_type_nums": 1, "attack_nums": 14, "start_time": "2025-08-26 11:06:34", "update_time": "2025-08-26 15:39:35"}, {"attack_ip": "*************", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 2, "attack_type_nums": 1, "attack_nums": 336, "start_time": "2025-08-25 08:02:29", "update_time": "2025-08-26 15:38:37"}, {"attack_ip": "************", "risk_level": 3, "ip_tags": ["内网恶意攻击"], "location": "局域网", "victim_ip_nums": 5, "attack_type_nums": 6, "attack_nums": 97, "start_time": "2025-08-25 16:17:50", "update_time": "2025-08-26 15:37:44"}, {"attack_ip": "***************", "risk_level": 3, "ip_tags": ["内网端口扫描", "内网恶意攻击", "暴力破解"], "location": "局域网", "victim_ip_nums": 30, "attack_type_nums": 1079, "attack_nums": 58440, "start_time": "2025-08-26 14:58:22", "update_time": "2025-08-26 15:34:10"}, {"attack_ip": "***********", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 2, "attack_type_nums": 2, "attack_nums": 155, "start_time": "2025-08-25 13:27:18", "update_time": "2025-08-26 15:33:16"}, {"attack_ip": "**************", "risk_level": 3, "ip_tags": ["内网端口扫描", "内网恶意攻击", "暴力破解"], "location": "局域网", "victim_ip_nums": 257, "attack_type_nums": 62, "attack_nums": 1176, "start_time": "2025-08-26 14:24:32", "update_time": "2025-08-26 15:32:50"}, {"attack_ip": "***********", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 1, "attack_type_nums": 3, "attack_nums": 106, "start_time": "2025-08-26 09:49:05", "update_time": "2025-08-26 15:31:50"}]

2025-08-26 15:42:08.509 [pool-6-thread-5] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:8...
2025-08-26 15:42:08.510 [pool-6-thread-5] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：0,等待任务: 0
2025-08-26 15:42:10.591 [pool-4-thread-1] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [lambda$0,81] - 最大连接：200 当前连接: 2
2025-08-26 15:42:12.053 [Thread-5] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:42:12.054 [Thread-5] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：1,等待任务: 0
2025-08-26 15:42:12.054 [async-task-pool106] INFO  c.r.f.a.e.PullDataEvent - [lambda$1,102] - 开始流量告警日志同步: 测试非凡设备1
2025-08-26 15:42:12.054 [async-task-pool100] INFO  c.r.f.a.e.PullDataEvent - [lambda$1,102] - 开始流量告警日志同步: 测试设备2
2025-08-26 15:42:12.245 [Thread-5] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 192毫秒
2025-08-26 15:42:12.705 [taskScheduler-13] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:42:12.705 [taskScheduler-13] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：1,等待任务: 0
2025-08-26 15:42:12.722 [Thread-8] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:42:12.722 [Thread-8] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：3,等待任务: 0
2025-08-26 15:42:12.723 [async-task-pool14] INFO  c.r.f.a.e.PullHostIntrusionAttackEvent - [lambda$1,83] - 开始获取主机入侵攻击数据: 测试设备2
2025-08-26 15:42:12.723 [async-task-pool71] INFO  c.r.f.a.e.PullHostIntrusionAttackEvent - [lambda$1,83] - 开始获取主机入侵攻击数据: 测试非凡设备1
2025-08-26 15:42:12.724 [async-task-pool14] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,874] - 开始拉取主机入侵攻击数据，参数：HostIntrusionAttackParam(accessToken=null, sip=null, dip=null, startDate=2025-08-25 15:40:02, endDate=2025-08-26 15:40:12, page=1, pSize=20, deviceConfigId=4)
2025-08-26 15:42:12.724 [async-task-pool71] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,874] - 开始拉取主机入侵攻击数据，参数：HostIntrusionAttackParam(accessToken=null, sip=null, dip=null, startDate=2025-08-25 15:40:02, endDate=2025-08-26 15:40:12, page=1, pSize=20, deviceConfigId=1)
2025-08-26 15:42:12.725 [async-task-pool14] INFO  c.r.f.a.d.HostIntrusionAttackParam - [getRequestBase,80] - 非凡API主机入侵攻击分页请求参数: https://***************:23000/v2/host-edr-intrusion?access_token=lst2BAzxJTQL0eSZ&start_date=2025-08-25%2015%3A40%3A02&end_date=2025-08-26%2015%3A40%3A12&page=1&p_size=20
2025-08-26 15:42:12.726 [async-task-pool71] INFO  c.r.f.a.d.HostIntrusionAttackParam - [getRequestBase,80] - 非凡API主机入侵攻击分页请求参数: https://***************:23000/v2/host-edr-intrusion?access_token=lst2BAzxJTQL0eSZ&start_date=2025-08-25%2015%3A40%3A02&end_date=2025-08-26%2015%3A40%3A12&page=1&p_size=20
2025-08-26 15:42:12.759 [async-task-pool116] INFO  c.r.f.c.FFSafeRequestComponent - [execRequest,88] - 非凡请求响应: [{"id": 1058, "block_ip": "*************", "location": ["美国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-22 00:58:57", "release_time": "2025-08-29 01:00:59", "remarks": ""}, {"id": 1057, "block_ip": "************", "location": ["法国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-21 13:55:52", "release_time": "2025-08-28 13:56:53", "remarks": ""}, {"id": 1056, "block_ip": "***********", "location": ["印度"], "action": "阻断", "conditions": ["外网恶意攻击"], "type": "流量检测", "create_time": "2025-08-20 12:44:55", "release_time": "2025-08-27 12:46:57", "remarks": ""}]

2025-08-26 15:42:12.763 [Thread-6] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:42:12.763 [async-task-pool72] INFO  c.r.f.c.FFSafeRequestComponent - [execRequest,88] - 非凡请求响应: [{"id": 1058, "block_ip": "*************", "location": ["美国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-22 00:58:57", "release_time": "2025-08-29 01:00:59", "remarks": ""}, {"id": 1057, "block_ip": "************", "location": ["法国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-21 13:55:52", "release_time": "2025-08-28 13:56:53", "remarks": ""}, {"id": 1056, "block_ip": "***********", "location": ["印度"], "action": "阻断", "conditions": ["外网恶意攻击"], "type": "流量检测", "create_time": "2025-08-20 12:44:55", "release_time": "2025-08-27 12:46:57", "remarks": ""}]

2025-08-26 15:42:12.763 [Thread-6] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：5,等待任务: 0
2025-08-26 15:42:12.772 [async-task-pool101] INFO  c.r.f.a.e.PullFilterLogEvent - [lambda$1,90] - 开始获取IP阻断日志: 测试非凡设备1
2025-08-26 15:42:12.772 [async-task-pool124] INFO  c.r.f.a.e.PullFilterLogEvent - [lambda$1,90] - 开始获取IP阻断日志: 测试设备2
2025-08-26 15:42:12.781 [async-task-pool14] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,987] - 主机入侵攻击总数: 0
2025-08-26 15:42:12.782 [async-task-pool14] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1010] - 第1页获取0条数据
2025-08-26 15:42:12.786 [async-task-pool14] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1014] - 当前页无数据，停止获取
2025-08-26 15:42:12.792 [async-task-pool14] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1083] - 拉取主机入侵攻击数据成功，但没有有效数据
2025-08-26 15:42:12.796 [async-task-pool71] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,987] - 主机入侵攻击总数: 0
2025-08-26 15:42:12.796 [taskScheduler-13] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 91毫秒
2025-08-26 15:42:12.799 [async-task-pool71] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1010] - 第1页获取0条数据
2025-08-26 15:42:12.801 [async-task-pool71] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1014] - 当前页无数据，停止获取
2025-08-26 15:42:12.801 [async-task-pool71] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1083] - 拉取主机入侵攻击数据成功，但没有有效数据
2025-08-26 15:42:12.815 [Thread-8] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 93毫秒
2025-08-26 15:42:12.858 [Thread-6] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 95毫秒
2025-08-26 15:42:13.387 [Thread-7] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:42:13.387 [Thread-7] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：1,等待任务: 0
2025-08-26 15:42:13.387 [async-task-pool83] INFO  c.r.f.a.e.PullFlowRiskAssetsEvent - [lambda$1,100] - 开始获取流量风险资产数据: 测试非凡设备1
2025-08-26 15:42:13.387 [async-task-pool117] INFO  c.r.f.a.e.PullFlowRiskAssetsEvent - [lambda$1,100] - 开始获取流量风险资产数据: 测试设备2
2025-08-26 15:42:13.388 [async-task-pool83] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,352] - 开始拉取流量风险资产数据，参数：FlowRiskAssetsParam(accessToken=null, riskType=all, startTime=2025-08-25 15:40:09, endTime=2025-08-26 15:40:13, page=1, pageSize=30, deviceConfigId=1)
2025-08-26 15:42:13.388 [async-task-pool117] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,352] - 开始拉取流量风险资产数据，参数：FlowRiskAssetsParam(accessToken=null, riskType=all, startTime=2025-08-25 15:40:09, endTime=2025-08-26 15:40:13, page=1, pageSize=30, deviceConfigId=4)
2025-08-26 15:42:13.390 [async-task-pool83] INFO  c.r.f.a.d.FlowRiskAssetsParam - [getRequestBase,49] - 非凡API流量风险资产请求参数: https://***************:23000/v2/flow-risk-assets?access_token=lst2BAzxJTQL0eSZ&risk_type=all&start_date=2025-08-25%2015%3A40%3A09&end_date=2025-08-26%2015%3A40%3A13&page=1&page_size=30
2025-08-26 15:42:13.392 [async-task-pool117] INFO  c.r.f.a.d.FlowRiskAssetsParam - [getRequestBase,49] - 非凡API流量风险资产请求参数: https://***************:23000/v2/flow-risk-assets?access_token=lst2BAzxJTQL0eSZ&risk_type=all&start_date=2025-08-25%2015%3A40%3A09&end_date=2025-08-26%2015%3A40%3A13&page=1&page_size=30
2025-08-26 15:42:13.459 [async-task-pool83] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,458] - 流量风险资产总数: 6
2025-08-26 15:42:13.462 [async-task-pool83] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,481] - 第1页获取6条数据
2025-08-26 15:42:13.462 [async-task-pool117] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,458] - 流量风险资产总数: 6
2025-08-26 15:42:13.462 [async-task-pool83] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,488] - 当前页数据量(6)少于页大小(30)，已获取完所有数据
2025-08-26 15:42:13.462 [async-task-pool117] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,481] - 第1页获取6条数据
2025-08-26 15:42:13.463 [async-task-pool117] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,488] - 当前页数据量(6)少于页大小(30)，已获取完所有数据
2025-08-26 15:42:13.488 [async-task-pool83] INFO  c.r.m.c.c.FfsafeClientService - [processBatch,662] - 批量更新6条流量风险资产数据
2025-08-26 15:42:13.492 [async-task-pool83] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,551] - 拉取流量风险资产数据完成，耗时104ms，共1页，共6条数据，处理结果: true
2025-08-26 15:42:13.495 [async-task-pool117] INFO  c.r.m.c.c.FfsafeClientService - [processBatch,662] - 批量更新6条流量风险资产数据
2025-08-26 15:42:13.496 [async-task-pool117] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,551] - 拉取流量风险资产数据完成，耗时108ms，共1页，共6条数据，处理结果: true
2025-08-26 15:42:13.504 [Thread-7] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 117毫秒
2025-08-26 15:42:20.591 [pool-4-thread-1] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [lambda$0,81] - 最大连接：200 当前连接: 2
2025-08-26 15:42:22.253 [Thread-5] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:42:22.254 [Thread-5] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：1,等待任务: 0
2025-08-26 15:42:22.254 [async-task-pool123] INFO  c.r.f.a.e.PullDataEvent - [lambda$1,102] - 开始流量告警日志同步: 测试设备2
2025-08-26 15:42:22.254 [async-task-pool122] INFO  c.r.f.a.e.PullDataEvent - [lambda$1,102] - 开始流量告警日志同步: 测试非凡设备1
2025-08-26 15:42:22.817 [taskScheduler-13] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:42:22.817 [taskScheduler-13] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：3,等待任务: 0
2025-08-26 15:42:22.837 [Thread-8] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:42:22.838 [Thread-8] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：5,等待任务: 0
2025-08-26 15:42:22.839 [async-task-pool120] INFO  c.r.f.a.e.PullHostIntrusionAttackEvent - [lambda$1,83] - 开始获取主机入侵攻击数据: 测试非凡设备1
2025-08-26 15:42:22.839 [async-task-pool118] INFO  c.r.f.a.e.PullHostIntrusionAttackEvent - [lambda$1,83] - 开始获取主机入侵攻击数据: 测试设备2
2025-08-26 15:42:22.839 [async-task-pool120] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,874] - 开始拉取主机入侵攻击数据，参数：HostIntrusionAttackParam(accessToken=null, sip=null, dip=null, startDate=2025-08-25 15:40:12, endDate=2025-08-26 15:40:22, page=1, pSize=20, deviceConfigId=1)
2025-08-26 15:42:22.839 [async-task-pool118] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,874] - 开始拉取主机入侵攻击数据，参数：HostIntrusionAttackParam(accessToken=null, sip=null, dip=null, startDate=2025-08-25 15:40:12, endDate=2025-08-26 15:40:22, page=1, pSize=20, deviceConfigId=4)
2025-08-26 15:42:22.845 [async-task-pool120] INFO  c.r.f.a.d.HostIntrusionAttackParam - [getRequestBase,80] - 非凡API主机入侵攻击分页请求参数: https://***************:23000/v2/host-edr-intrusion?access_token=lst2BAzxJTQL0eSZ&start_date=2025-08-25%2015%3A40%3A12&end_date=2025-08-26%2015%3A40%3A22&page=1&p_size=20
2025-08-26 15:42:22.846 [async-task-pool118] INFO  c.r.f.a.d.HostIntrusionAttackParam - [getRequestBase,80] - 非凡API主机入侵攻击分页请求参数: https://***************:23000/v2/host-edr-intrusion?access_token=lst2BAzxJTQL0eSZ&start_date=2025-08-25%2015%3A40%3A12&end_date=2025-08-26%2015%3A40%3A22&page=1&p_size=20
2025-08-26 15:42:22.876 [pool-6-thread-5] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 14秒367毫秒
2025-08-26 15:42:22.898 [Thread-6] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:42:22.902 [Thread-6] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：6,等待任务: 0
2025-08-26 15:42:22.904 [async-task-pool128] INFO  c.r.f.a.e.PullFilterLogEvent - [lambda$1,90] - 开始获取IP阻断日志: 测试非凡设备1
2025-08-26 15:42:22.904 [async-task-pool108] INFO  c.r.f.a.e.PullFilterLogEvent - [lambda$1,90] - 开始获取IP阻断日志: 测试设备2
2025-08-26 15:42:22.963 [async-task-pool133] INFO  c.r.f.c.FFSafeRequestComponent - [execRequest,88] - 非凡请求响应: [{"id": 1058, "block_ip": "*************", "location": ["美国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-22 00:58:57", "release_time": "2025-08-29 01:00:59", "remarks": ""}, {"id": 1057, "block_ip": "************", "location": ["法国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-21 13:55:52", "release_time": "2025-08-28 13:56:53", "remarks": ""}, {"id": 1056, "block_ip": "***********", "location": ["印度"], "action": "阻断", "conditions": ["外网恶意攻击"], "type": "流量检测", "create_time": "2025-08-20 12:44:55", "release_time": "2025-08-27 12:46:57", "remarks": ""}]

2025-08-26 15:42:22.969 [async-task-pool103] INFO  c.r.f.c.FFSafeRequestComponent - [execRequest,88] - 非凡请求响应: [{"id": 1058, "block_ip": "*************", "location": ["美国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-22 00:58:57", "release_time": "2025-08-29 01:00:59", "remarks": ""}, {"id": 1057, "block_ip": "************", "location": ["法国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-21 13:55:52", "release_time": "2025-08-28 13:56:53", "remarks": ""}, {"id": 1056, "block_ip": "***********", "location": ["印度"], "action": "阻断", "conditions": ["外网恶意攻击"], "type": "流量检测", "create_time": "2025-08-20 12:44:55", "release_time": "2025-08-27 12:46:57", "remarks": ""}]

2025-08-26 15:42:22.984 [async-task-pool120] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,987] - 主机入侵攻击总数: 0
2025-08-26 15:42:22.997 [async-task-pool120] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1010] - 第1页获取0条数据
2025-08-26 15:42:23.001 [async-task-pool120] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1014] - 当前页无数据，停止获取
2025-08-26 15:42:23.001 [async-task-pool118] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,987] - 主机入侵攻击总数: 0
2025-08-26 15:42:23.009 [async-task-pool120] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1083] - 拉取主机入侵攻击数据成功，但没有有效数据
2025-08-26 15:42:23.012 [async-task-pool118] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1010] - 第1页获取0条数据
2025-08-26 15:42:23.016 [async-task-pool118] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1014] - 当前页无数据，停止获取
2025-08-26 15:42:23.021 [async-task-pool118] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1083] - 拉取主机入侵攻击数据成功，但没有有效数据
2025-08-26 15:42:23.081 [taskScheduler-13] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 264毫秒
2025-08-26 15:42:23.096 [Thread-8] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 259毫秒
2025-08-26 15:42:23.097 [Thread-6] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 199毫秒
2025-08-26 15:42:23.512 [Thread-7] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:42:23.512 [Thread-7] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：2,等待任务: 0
2025-08-26 15:42:23.513 [async-task-pool139] INFO  c.r.f.a.e.PullFlowRiskAssetsEvent - [lambda$1,100] - 开始获取流量风险资产数据: 测试设备2
2025-08-26 15:42:23.513 [async-task-pool130] INFO  c.r.f.a.e.PullFlowRiskAssetsEvent - [lambda$1,100] - 开始获取流量风险资产数据: 测试非凡设备1
2025-08-26 15:42:23.514 [async-task-pool139] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,352] - 开始拉取流量风险资产数据，参数：FlowRiskAssetsParam(accessToken=null, riskType=all, startTime=2025-08-25 15:40:19, endTime=2025-08-26 15:40:23, page=1, pageSize=30, deviceConfigId=4)
2025-08-26 15:42:23.514 [async-task-pool130] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,352] - 开始拉取流量风险资产数据，参数：FlowRiskAssetsParam(accessToken=null, riskType=all, startTime=2025-08-25 15:40:19, endTime=2025-08-26 15:40:23, page=1, pageSize=30, deviceConfigId=1)
2025-08-26 15:42:23.516 [async-task-pool139] INFO  c.r.f.a.d.FlowRiskAssetsParam - [getRequestBase,49] - 非凡API流量风险资产请求参数: https://***************:23000/v2/flow-risk-assets?access_token=lst2BAzxJTQL0eSZ&risk_type=all&start_date=2025-08-25%2015%3A40%3A19&end_date=2025-08-26%2015%3A40%3A23&page=1&page_size=30
2025-08-26 15:42:23.516 [async-task-pool130] INFO  c.r.f.a.d.FlowRiskAssetsParam - [getRequestBase,49] - 非凡API流量风险资产请求参数: https://***************:23000/v2/flow-risk-assets?access_token=lst2BAzxJTQL0eSZ&risk_type=all&start_date=2025-08-25%2015%3A40%3A19&end_date=2025-08-26%2015%3A40%3A23&page=1&page_size=30
2025-08-26 15:42:23.544 [pool-6-thread-5] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sync,255] - 批量更新攻击者视角告警列开始: 8
2025-08-26 15:42:23.602 [async-task-pool130] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,458] - 流量风险资产总数: 6
2025-08-26 15:42:23.602 [async-task-pool139] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,458] - 流量风险资产总数: 6
2025-08-26 15:42:23.603 [async-task-pool130] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,481] - 第1页获取6条数据
2025-08-26 15:42:23.603 [async-task-pool139] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,481] - 第1页获取6条数据
2025-08-26 15:42:23.603 [async-task-pool130] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,488] - 当前页数据量(6)少于页大小(30)，已获取完所有数据
2025-08-26 15:42:23.604 [async-task-pool139] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,488] - 当前页数据量(6)少于页大小(30)，已获取完所有数据
2025-08-26 15:42:23.619 [pool-6-thread-5] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=*************, operationType=UPDATE
2025-08-26 15:42:23.621 [pool-6-thread-5] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=**************, operationType=UPDATE
2025-08-26 15:42:23.634 [pool-6-thread-5] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=*************, operationType=UPDATE
2025-08-26 15:42:23.641 [pool-6-thread-5] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=************, operationType=UPDATE
2025-08-26 15:42:23.647 [async-task-pool130] INFO  c.r.m.c.c.FfsafeClientService - [processBatch,662] - 批量更新6条流量风险资产数据
2025-08-26 15:42:23.647 [async-task-pool130] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,551] - 拉取流量风险资产数据完成，耗时133ms，共1页，共6条数据，处理结果: true
2025-08-26 15:42:23.650 [pool-6-thread-5] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=***************, operationType=UPDATE
2025-08-26 15:42:23.653 [async-task-pool139] INFO  c.r.m.c.c.FfsafeClientService - [processBatch,662] - 批量更新6条流量风险资产数据
2025-08-26 15:42:23.653 [pool-6-thread-5] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=***********, operationType=UPDATE
2025-08-26 15:42:23.653 [async-task-pool139] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,551] - 拉取流量风险资产数据完成，耗时139ms，共1页，共6条数据，处理结果: true
2025-08-26 15:42:23.654 [pool-6-thread-5] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=**************, operationType=UPDATE
2025-08-26 15:42:23.661 [pool-6-thread-5] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=***********, operationType=UPDATE
2025-08-26 15:42:23.662 [Thread-7] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 150毫秒
2025-08-26 15:42:23.672 [pool-6-thread-2] INFO  c.r.s.t.FfSafeFlowAlarmTask - [syncTask,46] - 开始同步攻击者告警数据
2025-08-26 15:42:23.689 [pool-5-thread-2] INFO  c.r.s.t.TblDeductionDetailTask - [syncThreaten,202] - 处理威胁告警扣分结束
2025-08-26 15:42:23.698 [Thread-5] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 1秒445毫秒
2025-08-26 15:42:23.700 [pool-5-thread-3] INFO  c.r.s.t.TblDeductionDetailTask - [syncThreaten,202] - 处理威胁告警扣分结束
2025-08-26 15:42:23.722 [pool-6-thread-2] INFO  c.r.f.c.FFSafeRequestComponent - [execRequest,88] - 非凡请求响应: [{"attack_ip": "*************", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 2, "attack_type_nums": 1, "attack_nums": 336, "start_time": "2025-08-25 08:02:29", "update_time": "2025-08-26 15:38:37"}, {"attack_ip": "************", "risk_level": 3, "ip_tags": ["内网恶意攻击"], "location": "局域网", "victim_ip_nums": 5, "attack_type_nums": 6, "attack_nums": 97, "start_time": "2025-08-25 16:17:50", "update_time": "2025-08-26 15:37:44"}, {"attack_ip": "***************", "risk_level": 3, "ip_tags": ["内网端口扫描", "内网恶意攻击", "暴力破解"], "location": "局域网", "victim_ip_nums": 30, "attack_type_nums": 1079, "attack_nums": 58440, "start_time": "2025-08-26 14:58:22", "update_time": "2025-08-26 15:34:10"}, {"attack_ip": "***********", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 2, "attack_type_nums": 2, "attack_nums": 155, "start_time": "2025-08-25 13:27:18", "update_time": "2025-08-26 15:33:16"}, {"attack_ip": "**************", "risk_level": 3, "ip_tags": ["内网端口扫描", "内网恶意攻击", "暴力破解"], "location": "局域网", "victim_ip_nums": 257, "attack_type_nums": 62, "attack_nums": 1176, "start_time": "2025-08-26 14:24:32", "update_time": "2025-08-26 15:32:50"}, {"attack_ip": "***********", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 1, "attack_type_nums": 3, "attack_nums": 106, "start_time": "2025-08-26 09:49:05", "update_time": "2025-08-26 15:31:50"}, {"attack_ip": "***********", "risk_level": 2, "ip_tags": ["踩中蜜罐", "内网可疑行为"], "location": "局域网", "victim_ip_nums": 2, "attack_type_nums": 6, "attack_nums": 115, "start_time": "2025-08-25 14:42:32", "update_time": "2025-08-26 15:30:35"}]

2025-08-26 15:42:23.739 [pool-6-thread-2] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:7...
2025-08-26 15:42:23.746 [pool-6-thread-2] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：0,等待任务: 0
2025-08-26 15:42:30.657 [pool-4-thread-1] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [lambda$0,81] - 最大连接：200 当前连接: 2
2025-08-26 15:42:33.097 [taskScheduler-13] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:42:33.102 [taskScheduler-13] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：1,等待任务: 0
2025-08-26 15:42:33.137 [Thread-6] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:42:33.138 [Thread-8] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:42:33.138 [Thread-6] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：3,等待任务: 0
2025-08-26 15:42:33.139 [Thread-8] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：3,等待任务: 0
2025-08-26 15:42:33.140 [async-task-pool110] INFO  c.r.f.a.e.PullFilterLogEvent - [lambda$1,90] - 开始获取IP阻断日志: 测试非凡设备1
2025-08-26 15:42:33.140 [async-task-pool119] INFO  c.r.f.a.e.PullFilterLogEvent - [lambda$1,90] - 开始获取IP阻断日志: 测试设备2
2025-08-26 15:42:33.140 [async-task-pool141] INFO  c.r.f.a.e.PullHostIntrusionAttackEvent - [lambda$1,83] - 开始获取主机入侵攻击数据: 测试非凡设备1
2025-08-26 15:42:33.153 [async-task-pool141] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,874] - 开始拉取主机入侵攻击数据，参数：HostIntrusionAttackParam(accessToken=null, sip=null, dip=null, startDate=2025-08-25 15:40:22, endDate=2025-08-26 15:40:33, page=1, pSize=20, deviceConfigId=1)
2025-08-26 15:42:33.141 [async-task-pool136] INFO  c.r.f.a.e.PullHostIntrusionAttackEvent - [lambda$1,83] - 开始获取主机入侵攻击数据: 测试设备2
2025-08-26 15:42:33.156 [async-task-pool136] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,874] - 开始拉取主机入侵攻击数据，参数：HostIntrusionAttackParam(accessToken=null, sip=null, dip=null, startDate=2025-08-25 15:40:22, endDate=2025-08-26 15:40:33, page=1, pSize=20, deviceConfigId=4)
2025-08-26 15:42:33.155 [async-task-pool141] INFO  c.r.f.a.d.HostIntrusionAttackParam - [getRequestBase,80] - 非凡API主机入侵攻击分页请求参数: https://***************:23000/v2/host-edr-intrusion?access_token=lst2BAzxJTQL0eSZ&start_date=2025-08-25%2015%3A40%3A22&end_date=2025-08-26%2015%3A40%3A33&page=1&p_size=20
2025-08-26 15:42:33.158 [async-task-pool136] INFO  c.r.f.a.d.HostIntrusionAttackParam - [getRequestBase,80] - 非凡API主机入侵攻击分页请求参数: https://***************:23000/v2/host-edr-intrusion?access_token=lst2BAzxJTQL0eSZ&start_date=2025-08-25%2015%3A40%3A22&end_date=2025-08-26%2015%3A40%3A33&page=1&p_size=20
2025-08-26 15:42:33.244 [async-task-pool127] INFO  c.r.f.c.FFSafeRequestComponent - [execRequest,88] - 非凡请求响应: [{"id": 1058, "block_ip": "*************", "location": ["美国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-22 00:58:57", "release_time": "2025-08-29 01:00:59", "remarks": ""}, {"id": 1057, "block_ip": "************", "location": ["法国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-21 13:55:52", "release_time": "2025-08-28 13:56:53", "remarks": ""}, {"id": 1056, "block_ip": "***********", "location": ["印度"], "action": "阻断", "conditions": ["外网恶意攻击"], "type": "流量检测", "create_time": "2025-08-20 12:44:55", "release_time": "2025-08-27 12:46:57", "remarks": ""}]

2025-08-26 15:42:33.250 [async-task-pool138] INFO  c.r.f.c.FFSafeRequestComponent - [execRequest,88] - 非凡请求响应: [{"id": 1058, "block_ip": "*************", "location": ["美国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-22 00:58:57", "release_time": "2025-08-29 01:00:59", "remarks": ""}, {"id": 1057, "block_ip": "************", "location": ["法国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-21 13:55:52", "release_time": "2025-08-28 13:56:53", "remarks": ""}, {"id": 1056, "block_ip": "***********", "location": ["印度"], "action": "阻断", "conditions": ["外网恶意攻击"], "type": "流量检测", "create_time": "2025-08-20 12:44:55", "release_time": "2025-08-27 12:46:57", "remarks": ""}]

2025-08-26 15:42:33.296 [async-task-pool141] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,987] - 主机入侵攻击总数: 0
2025-08-26 15:42:33.304 [async-task-pool141] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1010] - 第1页获取0条数据
2025-08-26 15:42:33.307 [async-task-pool141] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1014] - 当前页无数据，停止获取
2025-08-26 15:42:33.308 [async-task-pool141] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1083] - 拉取主机入侵攻击数据成功，但没有有效数据
2025-08-26 15:42:33.312 [async-task-pool136] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,987] - 主机入侵攻击总数: 0
2025-08-26 15:42:33.313 [async-task-pool136] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1010] - 第1页获取0条数据
2025-08-26 15:42:33.322 [async-task-pool136] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1014] - 当前页无数据，停止获取
2025-08-26 15:42:33.322 [async-task-pool136] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1083] - 拉取主机入侵攻击数据成功，但没有有效数据
2025-08-26 15:42:33.358 [taskScheduler-13] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 261毫秒
2025-08-26 15:42:33.378 [Thread-8] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 240毫秒
2025-08-26 15:42:33.412 [Thread-6] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 275毫秒
2025-08-26 15:42:33.685 [Thread-7] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:42:33.687 [Thread-7] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：1,等待任务: 0
2025-08-26 15:42:33.692 [async-task-pool97] INFO  c.r.f.a.e.PullFlowRiskAssetsEvent - [lambda$1,100] - 开始获取流量风险资产数据: 测试非凡设备1
2025-08-26 15:42:33.693 [async-task-pool97] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,352] - 开始拉取流量风险资产数据，参数：FlowRiskAssetsParam(accessToken=null, riskType=all, startTime=2025-08-25 15:40:29, endTime=2025-08-26 15:40:33, page=1, pageSize=30, deviceConfigId=1)
2025-08-26 15:42:33.693 [async-task-pool93] INFO  c.r.f.a.e.PullFlowRiskAssetsEvent - [lambda$1,100] - 开始获取流量风险资产数据: 测试设备2
2025-08-26 15:42:33.697 [async-task-pool97] INFO  c.r.f.a.d.FlowRiskAssetsParam - [getRequestBase,49] - 非凡API流量风险资产请求参数: https://***************:23000/v2/flow-risk-assets?access_token=lst2BAzxJTQL0eSZ&risk_type=all&start_date=2025-08-25%2015%3A40%3A29&end_date=2025-08-26%2015%3A40%3A33&page=1&page_size=30
2025-08-26 15:42:33.702 [async-task-pool93] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,352] - 开始拉取流量风险资产数据，参数：FlowRiskAssetsParam(accessToken=null, riskType=all, startTime=2025-08-25 15:40:29, endTime=2025-08-26 15:40:33, page=1, pageSize=30, deviceConfigId=4)
2025-08-26 15:42:33.711 [async-task-pool93] INFO  c.r.f.a.d.FlowRiskAssetsParam - [getRequestBase,49] - 非凡API流量风险资产请求参数: https://***************:23000/v2/flow-risk-assets?access_token=lst2BAzxJTQL0eSZ&risk_type=all&start_date=2025-08-25%2015%3A40%3A29&end_date=2025-08-26%2015%3A40%3A33&page=1&page_size=30
2025-08-26 15:42:33.742 [Thread-5] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:42:33.744 [Thread-5] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：3,等待任务: 0
2025-08-26 15:42:33.745 [async-task-pool154] INFO  c.r.f.a.e.PullDataEvent - [lambda$1,102] - 开始流量告警日志同步: 测试设备2
2025-08-26 15:42:33.745 [async-task-pool113] INFO  c.r.f.a.e.PullDataEvent - [lambda$1,102] - 开始流量告警日志同步: 测试非凡设备1
2025-08-26 15:42:33.820 [async-task-pool93] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,458] - 流量风险资产总数: 6
2025-08-26 15:42:33.823 [async-task-pool93] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,481] - 第1页获取6条数据
2025-08-26 15:42:33.824 [async-task-pool93] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,488] - 当前页数据量(6)少于页大小(30)，已获取完所有数据
2025-08-26 15:42:33.826 [async-task-pool97] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,458] - 流量风险资产总数: 6
2025-08-26 15:42:33.827 [async-task-pool97] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,481] - 第1页获取6条数据
2025-08-26 15:42:33.828 [async-task-pool97] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,488] - 当前页数据量(6)少于页大小(30)，已获取完所有数据
2025-08-26 15:42:33.855 [async-task-pool93] INFO  c.r.m.c.c.FfsafeClientService - [processBatch,662] - 批量更新6条流量风险资产数据
2025-08-26 15:42:33.859 [async-task-pool93] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,551] - 拉取流量风险资产数据完成，耗时157ms，共1页，共6条数据，处理结果: true
2025-08-26 15:42:33.865 [async-task-pool97] INFO  c.r.m.c.c.FfsafeClientService - [processBatch,662] - 批量更新6条流量风险资产数据
2025-08-26 15:42:33.870 [async-task-pool97] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,551] - 拉取流量风险资产数据完成，耗时177ms，共1页，共6条数据，处理结果: true
2025-08-26 15:42:33.876 [Thread-7] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 191毫秒
2025-08-26 15:42:33.946 [Thread-5] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 204毫秒
2025-08-26 15:42:34.528 [taskScheduler-3] INFO  c.r.r.s.HandleDataSyncSender - [sendDataSync,230] - 数据同步发送成功, messageId: 184c2230-ca98-40a7-b745-1a79dc1f050f, 加密状态: 已加密, 消息类型: PING
2025-08-26 15:42:34.529 [taskScheduler-3] INFO  c.r.r.t.RabbitMQTask - [ping,44] - 发送心跳消息
2025-08-26 15:42:34.536 [rabbitConnectionFactory10] INFO  c.r.f.c.r.RabbitMQConfig - [lambda$0,290] - 消息已成功发送到交换机, messageId: 184c2230-ca98-40a7-b745-1a79dc1f050f
2025-08-26 15:42:35.108 [taskScheduler-42] INFO  c.r.c.task.HlsTask - [checkHls,65] - hls检查开始...
2025-08-26 15:42:35.113 [taskScheduler-42] INFO  c.r.c.task.HlsTask - [checkHls,86] - hls检查结束...
2025-08-26 15:42:39.062 [pool-6-thread-2] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 15秒323毫秒
2025-08-26 15:42:40.187 [pool-6-thread-2] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sync,255] - 批量更新攻击者视角告警列开始: 7
2025-08-26 15:42:40.252 [pool-6-thread-2] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=*************, operationType=UPDATE
2025-08-26 15:42:40.257 [pool-6-thread-2] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=************, operationType=UPDATE
2025-08-26 15:42:40.263 [pool-6-thread-2] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=***************, operationType=UPDATE
2025-08-26 15:42:40.266 [pool-6-thread-2] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=***********, operationType=UPDATE
2025-08-26 15:42:40.268 [pool-6-thread-2] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=**************, operationType=UPDATE
2025-08-26 15:42:40.270 [pool-6-thread-2] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=***********, operationType=UPDATE
2025-08-26 15:42:40.279 [pool-6-thread-2] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=***********, operationType=UPDATE
2025-08-26 15:42:40.284 [pool-6-thread-5] INFO  c.r.s.t.FfSafeFlowAlarmTask - [syncTask,46] - 开始同步攻击者告警数据
2025-08-26 15:42:40.364 [pool-6-thread-5] INFO  c.r.f.c.FFSafeRequestComponent - [execRequest,88] - 非凡请求响应: [{"attack_ip": "**************", "risk_level": 3, "ip_tags": ["暴力破解"], "location": "局域网", "victim_ip_nums": 9, "attack_type_nums": 9, "attack_nums": 14936, "start_time": "2025-08-25 08:00:02", "update_time": "2025-08-26 15:42:21"}, {"attack_ip": "**************", "risk_level": 3, "ip_tags": ["非法外联"], "location": "局域网", "victim_ip_nums": 2, "attack_type_nums": 2, "attack_nums": 176237, "start_time": "2025-08-25 08:00:04", "update_time": "2025-08-26 15:42:16"}, {"attack_ip": "**************", "risk_level": 2, "ip_tags": ["弱口令登录成功"], "location": "局域网", "victim_ip_nums": 2, "attack_type_nums": 5, "attack_nums": 7385, "start_time": "2025-08-25 08:00:02", "update_time": "2025-08-26 15:42:10"}, {"attack_ip": "************", "risk_level": 3, "ip_tags": ["内网恶意攻击"], "location": "局域网", "victim_ip_nums": 9, "attack_type_nums": 36, "attack_nums": 418, "start_time": "2025-08-25 08:12:47", "update_time": "2025-08-26 15:42:03"}, {"attack_ip": "***********", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 1, "attack_type_nums": 1, "attack_nums": 259, "start_time": "2025-08-26 09:54:16", "update_time": "2025-08-26 15:41:44"}, {"attack_ip": "**********", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 1, "attack_type_nums": 1, "attack_nums": 404, "start_time": "2025-08-25 08:14:50", "update_time": "2025-08-26 15:41:42"}, {"attack_ip": "***********", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 1, "attack_type_nums": 1, "attack_nums": 279, "start_time": "2025-08-25 08:27:55", "update_time": "2025-08-26 15:41:42"}, {"attack_ip": "**********0", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 1, "attack_type_nums": 1, "attack_nums": 586, "start_time": "2025-08-25 08:59:16", "update_time": "2025-08-26 15:41:31"}, {"attack_ip": "*************", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 2, "attack_type_nums": 1, "attack_nums": 333, "start_time": "2025-08-25 08:03:53", "update_time": "2025-08-26 15:39:58"}, {"attack_ip": "**************", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 1, "attack_type_nums": 1, "attack_nums": 14, "start_time": "2025-08-26 11:06:34", "update_time": "2025-08-26 15:39:35"}, {"attack_ip": "*************", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 2, "attack_type_nums": 1, "attack_nums": 336, "start_time": "2025-08-25 08:02:29", "update_time": "2025-08-26 15:38:37"}, {"attack_ip": "************", "risk_level": 3, "ip_tags": ["内网恶意攻击"], "location": "局域网", "victim_ip_nums": 5, "attack_type_nums": 6, "attack_nums": 97, "start_time": "2025-08-25 16:17:50", "update_time": "2025-08-26 15:37:44"}, {"attack_ip": "***************", "risk_level": 3, "ip_tags": ["内网端口扫描", "内网恶意攻击", "暴力破解"], "location": "局域网", "victim_ip_nums": 30, "attack_type_nums": 1079, "attack_nums": 58440, "start_time": "2025-08-26 14:58:22", "update_time": "2025-08-26 15:34:10"}, {"attack_ip": "***********", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 2, "attack_type_nums": 2, "attack_nums": 155, "start_time": "2025-08-25 13:27:18", "update_time": "2025-08-26 15:33:16"}, {"attack_ip": "**************", "risk_level": 3, "ip_tags": ["内网端口扫描", "内网恶意攻击", "暴力破解"], "location": "局域网", "victim_ip_nums": 257, "attack_type_nums": 62, "attack_nums": 1176, "start_time": "2025-08-26 14:24:32", "update_time": "2025-08-26 15:32:50"}]

2025-08-26 15:42:40.399 [pool-6-thread-5] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:15...
2025-08-26 15:42:40.399 [pool-6-thread-5] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：0,等待任务: 0
2025-08-26 15:42:40.895 [pool-4-thread-1] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [lambda$0,81] - 最大连接：200 当前连接: 6
2025-08-26 15:42:43.386 [taskScheduler-14] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:42:43.387 [taskScheduler-14] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：4,等待任务: 0
2025-08-26 15:42:43.402 [Thread-8] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:42:43.403 [Thread-8] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：5,等待任务: 0
2025-08-26 15:42:43.403 [async-task-pool142] INFO  c.r.f.a.e.PullHostIntrusionAttackEvent - [lambda$1,83] - 开始获取主机入侵攻击数据: 测试非凡设备1
2025-08-26 15:42:43.403 [async-task-pool153] INFO  c.r.f.a.e.PullHostIntrusionAttackEvent - [lambda$1,83] - 开始获取主机入侵攻击数据: 测试设备2
2025-08-26 15:42:43.404 [async-task-pool153] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,874] - 开始拉取主机入侵攻击数据，参数：HostIntrusionAttackParam(accessToken=null, sip=null, dip=null, startDate=2025-08-25 15:40:33, endDate=2025-08-26 15:40:43, page=1, pSize=20, deviceConfigId=4)
2025-08-26 15:42:43.404 [async-task-pool142] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,874] - 开始拉取主机入侵攻击数据，参数：HostIntrusionAttackParam(accessToken=null, sip=null, dip=null, startDate=2025-08-25 15:40:33, endDate=2025-08-26 15:40:43, page=1, pSize=20, deviceConfigId=1)
2025-08-26 15:42:43.407 [async-task-pool153] INFO  c.r.f.a.d.HostIntrusionAttackParam - [getRequestBase,80] - 非凡API主机入侵攻击分页请求参数: https://***************:23000/v2/host-edr-intrusion?access_token=lst2BAzxJTQL0eSZ&start_date=2025-08-25%2015%3A40%3A33&end_date=2025-08-26%2015%3A40%3A43&page=1&p_size=20
2025-08-26 15:42:43.411 [async-task-pool142] INFO  c.r.f.a.d.HostIntrusionAttackParam - [getRequestBase,80] - 非凡API主机入侵攻击分页请求参数: https://***************:23000/v2/host-edr-intrusion?access_token=lst2BAzxJTQL0eSZ&start_date=2025-08-25%2015%3A40%3A33&end_date=2025-08-26%2015%3A40%3A43&page=1&p_size=20
2025-08-26 15:42:43.434 [Thread-6] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:42:43.434 [Thread-6] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：7,等待任务: 0
2025-08-26 15:42:43.434 [async-task-pool164] INFO  c.r.f.a.e.PullFilterLogEvent - [lambda$1,90] - 开始获取IP阻断日志: 测试设备2
2025-08-26 15:42:43.434 [async-task-pool168] INFO  c.r.f.a.e.PullFilterLogEvent - [lambda$1,90] - 开始获取IP阻断日志: 测试非凡设备1
2025-08-26 15:42:43.482 [async-task-pool167] INFO  c.r.f.c.FFSafeRequestComponent - [execRequest,88] - 非凡请求响应: [{"id": 1058, "block_ip": "*************", "location": ["美国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-22 00:58:57", "release_time": "2025-08-29 01:00:59", "remarks": ""}, {"id": 1057, "block_ip": "************", "location": ["法国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-21 13:55:52", "release_time": "2025-08-28 13:56:53", "remarks": ""}, {"id": 1056, "block_ip": "***********", "location": ["印度"], "action": "阻断", "conditions": ["外网恶意攻击"], "type": "流量检测", "create_time": "2025-08-20 12:44:55", "release_time": "2025-08-27 12:46:57", "remarks": ""}]

2025-08-26 15:42:43.494 [async-task-pool156] INFO  c.r.f.c.FFSafeRequestComponent - [execRequest,88] - 非凡请求响应: [{"id": 1058, "block_ip": "*************", "location": ["美国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-22 00:58:57", "release_time": "2025-08-29 01:00:59", "remarks": ""}, {"id": 1057, "block_ip": "************", "location": ["法国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-21 13:55:52", "release_time": "2025-08-28 13:56:53", "remarks": ""}, {"id": 1056, "block_ip": "***********", "location": ["印度"], "action": "阻断", "conditions": ["外网恶意攻击"], "type": "流量检测", "create_time": "2025-08-20 12:44:55", "release_time": "2025-08-27 12:46:57", "remarks": ""}]

2025-08-26 15:42:43.495 [async-task-pool153] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,987] - 主机入侵攻击总数: 0
2025-08-26 15:42:43.496 [async-task-pool153] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1010] - 第1页获取0条数据
2025-08-26 15:42:43.498 [async-task-pool153] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1014] - 当前页无数据，停止获取
2025-08-26 15:42:43.499 [async-task-pool153] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1083] - 拉取主机入侵攻击数据成功，但没有有效数据
2025-08-26 15:42:43.501 [async-task-pool142] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,987] - 主机入侵攻击总数: 0
2025-08-26 15:42:43.504 [async-task-pool142] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1010] - 第1页获取0条数据
2025-08-26 15:42:43.505 [async-task-pool142] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1014] - 当前页无数据，停止获取
2025-08-26 15:42:43.506 [async-task-pool142] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1083] - 拉取主机入侵攻击数据成功，但没有有效数据
2025-08-26 15:42:43.518 [taskScheduler-14] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 132毫秒
2025-08-26 15:42:43.532 [Thread-6] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 98毫秒
2025-08-26 15:42:43.534 [Thread-8] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 132毫秒
2025-08-26 15:42:43.893 [Thread-7] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:42:43.893 [Thread-7] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：3,等待任务: 0
2025-08-26 15:42:43.893 [async-task-pool170] INFO  c.r.f.a.e.PullFlowRiskAssetsEvent - [lambda$1,100] - 开始获取流量风险资产数据: 测试设备2
2025-08-26 15:42:43.893 [async-task-pool157] INFO  c.r.f.a.e.PullFlowRiskAssetsEvent - [lambda$1,100] - 开始获取流量风险资产数据: 测试非凡设备1
2025-08-26 15:42:43.894 [async-task-pool170] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,352] - 开始拉取流量风险资产数据，参数：FlowRiskAssetsParam(accessToken=null, riskType=all, startTime=2025-08-25 15:40:39, endTime=2025-08-26 15:40:43, page=1, pageSize=30, deviceConfigId=4)
2025-08-26 15:42:43.895 [async-task-pool157] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,352] - 开始拉取流量风险资产数据，参数：FlowRiskAssetsParam(accessToken=null, riskType=all, startTime=2025-08-25 15:40:39, endTime=2025-08-26 15:40:43, page=1, pageSize=30, deviceConfigId=1)
2025-08-26 15:42:43.896 [async-task-pool170] INFO  c.r.f.a.d.FlowRiskAssetsParam - [getRequestBase,49] - 非凡API流量风险资产请求参数: https://***************:23000/v2/flow-risk-assets?access_token=lst2BAzxJTQL0eSZ&risk_type=all&start_date=2025-08-25%2015%3A40%3A39&end_date=2025-08-26%2015%3A40%3A43&page=1&page_size=30
2025-08-26 15:42:43.898 [async-task-pool157] INFO  c.r.f.a.d.FlowRiskAssetsParam - [getRequestBase,49] - 非凡API流量风险资产请求参数: https://***************:23000/v2/flow-risk-assets?access_token=lst2BAzxJTQL0eSZ&risk_type=all&start_date=2025-08-25%2015%3A40%3A39&end_date=2025-08-26%2015%3A40%3A43&page=1&page_size=30
2025-08-26 15:42:43.954 [Thread-5] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:42:43.954 [Thread-5] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：5,等待任务: 0
2025-08-26 15:42:43.954 [async-task-pool180] INFO  c.r.f.a.e.PullDataEvent - [lambda$1,102] - 开始流量告警日志同步: 测试设备2
2025-08-26 15:42:43.954 [async-task-pool129] INFO  c.r.f.a.e.PullDataEvent - [lambda$1,102] - 开始流量告警日志同步: 测试非凡设备1
2025-08-26 15:42:43.973 [async-task-pool170] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,458] - 流量风险资产总数: 6
2025-08-26 15:42:43.977 [async-task-pool170] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,481] - 第1页获取6条数据
2025-08-26 15:42:43.978 [async-task-pool170] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,488] - 当前页数据量(6)少于页大小(30)，已获取完所有数据
2025-08-26 15:42:43.979 [async-task-pool157] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,458] - 流量风险资产总数: 6
2025-08-26 15:42:43.980 [async-task-pool157] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,481] - 第1页获取6条数据
2025-08-26 15:42:43.982 [async-task-pool157] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,488] - 当前页数据量(6)少于页大小(30)，已获取完所有数据
2025-08-26 15:42:44.001 [async-task-pool170] INFO  c.r.m.c.c.FfsafeClientService - [processBatch,662] - 批量更新6条流量风险资产数据
2025-08-26 15:42:44.004 [async-task-pool170] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,551] - 拉取流量风险资产数据完成，耗时110ms，共1页，共6条数据，处理结果: true
2025-08-26 15:42:44.011 [async-task-pool157] INFO  c.r.m.c.c.FfsafeClientService - [processBatch,662] - 批量更新6条流量风险资产数据
2025-08-26 15:42:44.012 [async-task-pool157] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,551] - 拉取流量风险资产数据完成，耗时117ms，共1页，共6条数据，处理结果: true
2025-08-26 15:42:44.017 [Thread-7] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 124毫秒
2025-08-26 15:42:44.116 [Thread-5] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 162毫秒
2025-08-26 15:42:50.896 [pool-4-thread-1] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [lambda$0,81] - 最大连接：200 当前连接: 3
2025-08-26 15:42:53.537 [taskScheduler-14] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:42:53.537 [Thread-6] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:42:53.538 [Thread-8] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:42:53.538 [taskScheduler-14] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：2,等待任务: 0
2025-08-26 15:42:53.538 [Thread-6] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：2,等待任务: 0
2025-08-26 15:42:53.539 [Thread-8] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：2,等待任务: 0
2025-08-26 15:42:53.540 [async-task-pool166] INFO  c.r.f.a.e.PullFilterLogEvent - [lambda$1,90] - 开始获取IP阻断日志: 测试非凡设备1
2025-08-26 15:42:53.540 [async-task-pool144] INFO  c.r.f.a.e.PullFilterLogEvent - [lambda$1,90] - 开始获取IP阻断日志: 测试设备2
2025-08-26 15:42:53.541 [async-task-pool171] INFO  c.r.f.a.e.PullHostIntrusionAttackEvent - [lambda$1,83] - 开始获取主机入侵攻击数据: 测试非凡设备1
2025-08-26 15:42:53.541 [async-task-pool172] INFO  c.r.f.a.e.PullHostIntrusionAttackEvent - [lambda$1,83] - 开始获取主机入侵攻击数据: 测试设备2
2025-08-26 15:42:53.542 [async-task-pool171] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,874] - 开始拉取主机入侵攻击数据，参数：HostIntrusionAttackParam(accessToken=null, sip=null, dip=null, startDate=2025-08-25 15:40:43, endDate=2025-08-26 15:40:53, page=1, pSize=20, deviceConfigId=1)
2025-08-26 15:42:53.543 [async-task-pool172] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,874] - 开始拉取主机入侵攻击数据，参数：HostIntrusionAttackParam(accessToken=null, sip=null, dip=null, startDate=2025-08-25 15:40:43, endDate=2025-08-26 15:40:53, page=1, pSize=20, deviceConfigId=4)
2025-08-26 15:42:53.545 [async-task-pool171] INFO  c.r.f.a.d.HostIntrusionAttackParam - [getRequestBase,80] - 非凡API主机入侵攻击分页请求参数: https://***************:23000/v2/host-edr-intrusion?access_token=lst2BAzxJTQL0eSZ&start_date=2025-08-25%2015%3A40%3A43&end_date=2025-08-26%2015%3A40%3A53&page=1&p_size=20
2025-08-26 15:42:53.548 [async-task-pool172] INFO  c.r.f.a.d.HostIntrusionAttackParam - [getRequestBase,80] - 非凡API主机入侵攻击分页请求参数: https://***************:23000/v2/host-edr-intrusion?access_token=lst2BAzxJTQL0eSZ&start_date=2025-08-25%2015%3A40%3A43&end_date=2025-08-26%2015%3A40%3A53&page=1&p_size=20
2025-08-26 15:42:53.605 [async-task-pool177] INFO  c.r.f.c.FFSafeRequestComponent - [execRequest,88] - 非凡请求响应: [{"id": 1058, "block_ip": "*************", "location": ["美国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-22 00:58:57", "release_time": "2025-08-29 01:00:59", "remarks": ""}, {"id": 1057, "block_ip": "************", "location": ["法国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-21 13:55:52", "release_time": "2025-08-28 13:56:53", "remarks": ""}, {"id": 1056, "block_ip": "***********", "location": ["印度"], "action": "阻断", "conditions": ["外网恶意攻击"], "type": "流量检测", "create_time": "2025-08-20 12:44:55", "release_time": "2025-08-27 12:46:57", "remarks": ""}]

2025-08-26 15:42:53.612 [async-task-pool171] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,987] - 主机入侵攻击总数: 0
2025-08-26 15:42:53.613 [async-task-pool171] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1010] - 第1页获取0条数据
2025-08-26 15:42:53.613 [async-task-pool171] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1014] - 当前页无数据，停止获取
2025-08-26 15:42:53.614 [async-task-pool171] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1083] - 拉取主机入侵攻击数据成功，但没有有效数据
2025-08-26 15:42:53.614 [Thread-6] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 77毫秒
2025-08-26 15:42:53.621 [async-task-pool158] INFO  c.r.f.c.FFSafeRequestComponent - [execRequest,88] - 非凡请求响应: [{"id": 1058, "block_ip": "*************", "location": ["美国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-22 00:58:57", "release_time": "2025-08-29 01:00:59", "remarks": ""}, {"id": 1057, "block_ip": "************", "location": ["法国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-21 13:55:52", "release_time": "2025-08-28 13:56:53", "remarks": ""}, {"id": 1056, "block_ip": "***********", "location": ["印度"], "action": "阻断", "conditions": ["外网恶意攻击"], "type": "流量检测", "create_time": "2025-08-20 12:44:55", "release_time": "2025-08-27 12:46:57", "remarks": ""}]

2025-08-26 15:42:53.633 [async-task-pool172] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,987] - 主机入侵攻击总数: 0
2025-08-26 15:42:53.636 [async-task-pool172] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1010] - 第1页获取0条数据
2025-08-26 15:42:53.636 [async-task-pool172] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1014] - 当前页无数据，停止获取
2025-08-26 15:42:53.637 [async-task-pool172] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1083] - 拉取主机入侵攻击数据成功，但没有有效数据
2025-08-26 15:42:53.639 [taskScheduler-14] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 102毫秒
2025-08-26 15:42:53.647 [Thread-8] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 109毫秒
2025-08-26 15:42:54.030 [Thread-7] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:42:54.030 [Thread-7] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：2,等待任务: 0
2025-08-26 15:42:54.031 [async-task-pool196] INFO  c.r.f.a.e.PullFlowRiskAssetsEvent - [lambda$1,100] - 开始获取流量风险资产数据: 测试设备2
2025-08-26 15:42:54.031 [async-task-pool185] INFO  c.r.f.a.e.PullFlowRiskAssetsEvent - [lambda$1,100] - 开始获取流量风险资产数据: 测试非凡设备1
2025-08-26 15:42:54.031 [async-task-pool196] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,352] - 开始拉取流量风险资产数据，参数：FlowRiskAssetsParam(accessToken=null, riskType=all, startTime=2025-08-25 15:40:49, endTime=2025-08-26 15:40:54, page=1, pageSize=30, deviceConfigId=4)
2025-08-26 15:42:54.031 [async-task-pool185] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,352] - 开始拉取流量风险资产数据，参数：FlowRiskAssetsParam(accessToken=null, riskType=all, startTime=2025-08-25 15:40:49, endTime=2025-08-26 15:40:54, page=1, pageSize=30, deviceConfigId=1)
2025-08-26 15:42:54.033 [async-task-pool196] INFO  c.r.f.a.d.FlowRiskAssetsParam - [getRequestBase,49] - 非凡API流量风险资产请求参数: https://***************:23000/v2/flow-risk-assets?access_token=lst2BAzxJTQL0eSZ&risk_type=all&start_date=2025-08-25%2015%3A40%3A49&end_date=2025-08-26%2015%3A40%3A54&page=1&page_size=30
2025-08-26 15:42:54.033 [async-task-pool185] INFO  c.r.f.a.d.FlowRiskAssetsParam - [getRequestBase,49] - 非凡API流量风险资产请求参数: https://***************:23000/v2/flow-risk-assets?access_token=lst2BAzxJTQL0eSZ&risk_type=all&start_date=2025-08-25%2015%3A40%3A49&end_date=2025-08-26%2015%3A40%3A54&page=1&page_size=30
2025-08-26 15:42:54.120 [async-task-pool196] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,458] - 流量风险资产总数: 6
2025-08-26 15:42:54.120 [async-task-pool196] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,481] - 第1页获取6条数据
2025-08-26 15:42:54.121 [async-task-pool196] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,488] - 当前页数据量(6)少于页大小(30)，已获取完所有数据
2025-08-26 15:42:54.124 [Thread-5] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:42:54.124 [Thread-5] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：4,等待任务: 0
2025-08-26 15:42:54.125 [async-task-pool197] INFO  c.r.f.a.e.PullDataEvent - [lambda$1,102] - 开始流量告警日志同步: 测试非凡设备1
2025-08-26 15:42:54.125 [async-task-pool151] INFO  c.r.f.a.e.PullDataEvent - [lambda$1,102] - 开始流量告警日志同步: 测试设备2
2025-08-26 15:42:54.133 [async-task-pool185] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,458] - 流量风险资产总数: 6
2025-08-26 15:42:54.143 [async-task-pool185] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,481] - 第1页获取6条数据
2025-08-26 15:42:54.143 [async-task-pool185] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,488] - 当前页数据量(6)少于页大小(30)，已获取完所有数据
2025-08-26 15:42:54.147 [async-task-pool196] INFO  c.r.m.c.c.FfsafeClientService - [processBatch,662] - 批量更新6条流量风险资产数据
2025-08-26 15:42:54.148 [async-task-pool196] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,551] - 拉取流量风险资产数据完成，耗时117ms，共1页，共6条数据，处理结果: true
2025-08-26 15:42:54.170 [async-task-pool185] INFO  c.r.m.c.c.FfsafeClientService - [processBatch,662] - 批量更新6条流量风险资产数据
2025-08-26 15:42:54.171 [async-task-pool185] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,551] - 拉取流量风险资产数据完成，耗时140ms，共1页，共6条数据，处理结果: true
2025-08-26 15:42:54.177 [Thread-7] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 147毫秒
2025-08-26 15:42:54.973 [Thread-5] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 849毫秒
2025-08-26 15:43:00.942 [pool-4-thread-1] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [lambda$0,81] - 最大连接：200 当前连接: 2
2025-08-26 15:43:03.801 [Thread-8] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:43:03.801 [taskScheduler-14] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:43:03.801 [Thread-6] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:43:03.802 [Thread-8] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：1,等待任务: 0
2025-08-26 15:43:03.802 [taskScheduler-14] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：1,等待任务: 0
2025-08-26 15:43:03.853 [Thread-6] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：1,等待任务: 0
2025-08-26 15:43:03.856 [async-task-pool188] INFO  c.r.f.a.e.PullHostIntrusionAttackEvent - [lambda$1,83] - 开始获取主机入侵攻击数据: 测试非凡设备1
2025-08-26 15:43:03.882 [async-task-pool188] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,874] - 开始拉取主机入侵攻击数据，参数：HostIntrusionAttackParam(accessToken=null, sip=null, dip=null, startDate=2025-08-25 15:40:53, endDate=2025-08-26 15:41:03, page=1, pSize=20, deviceConfigId=1)
2025-08-26 15:43:03.856 [async-task-pool183] INFO  c.r.f.a.e.PullHostIntrusionAttackEvent - [lambda$1,83] - 开始获取主机入侵攻击数据: 测试设备2
2025-08-26 15:43:03.883 [async-task-pool188] INFO  c.r.f.a.d.HostIntrusionAttackParam - [getRequestBase,80] - 非凡API主机入侵攻击分页请求参数: https://***************:23000/v2/host-edr-intrusion?access_token=lst2BAzxJTQL0eSZ&start_date=2025-08-25%2015%3A40%3A53&end_date=2025-08-26%2015%3A41%3A03&page=1&p_size=20
2025-08-26 15:43:03.873 [async-task-pool176] INFO  c.r.f.a.e.PullFilterLogEvent - [lambda$1,90] - 开始获取IP阻断日志: 测试非凡设备1
2025-08-26 15:43:03.874 [async-task-pool140] INFO  c.r.f.a.e.PullFilterLogEvent - [lambda$1,90] - 开始获取IP阻断日志: 测试设备2
2025-08-26 15:43:03.884 [async-task-pool183] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,874] - 开始拉取主机入侵攻击数据，参数：HostIntrusionAttackParam(accessToken=null, sip=null, dip=null, startDate=2025-08-25 15:40:53, endDate=2025-08-26 15:41:03, page=1, pSize=20, deviceConfigId=4)
2025-08-26 15:43:03.887 [async-task-pool183] INFO  c.r.f.a.d.HostIntrusionAttackParam - [getRequestBase,80] - 非凡API主机入侵攻击分页请求参数: https://***************:23000/v2/host-edr-intrusion?access_token=lst2BAzxJTQL0eSZ&start_date=2025-08-25%2015%3A40%3A53&end_date=2025-08-26%2015%3A41%3A03&page=1&p_size=20
2025-08-26 15:43:03.984 [async-task-pool150] INFO  c.r.f.c.FFSafeRequestComponent - [execRequest,88] - 非凡请求响应: [{"id": 1058, "block_ip": "*************", "location": ["美国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-22 00:58:57", "release_time": "2025-08-29 01:00:59", "remarks": ""}, {"id": 1057, "block_ip": "************", "location": ["法国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-21 13:55:52", "release_time": "2025-08-28 13:56:53", "remarks": ""}, {"id": 1056, "block_ip": "***********", "location": ["印度"], "action": "阻断", "conditions": ["外网恶意攻击"], "type": "流量检测", "create_time": "2025-08-20 12:44:55", "release_time": "2025-08-27 12:46:57", "remarks": ""}]

2025-08-26 15:43:03.997 [async-task-pool148] INFO  c.r.f.c.FFSafeRequestComponent - [execRequest,88] - 非凡请求响应: [{"id": 1058, "block_ip": "*************", "location": ["美国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-22 00:58:57", "release_time": "2025-08-29 01:00:59", "remarks": ""}, {"id": 1057, "block_ip": "************", "location": ["法国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-21 13:55:52", "release_time": "2025-08-28 13:56:53", "remarks": ""}, {"id": 1056, "block_ip": "***********", "location": ["印度"], "action": "阻断", "conditions": ["外网恶意攻击"], "type": "流量检测", "create_time": "2025-08-20 12:44:55", "release_time": "2025-08-27 12:46:57", "remarks": ""}]

2025-08-26 15:43:04.003 [async-task-pool188] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,987] - 主机入侵攻击总数: 0
2025-08-26 15:43:04.004 [async-task-pool188] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1010] - 第1页获取0条数据
2025-08-26 15:43:04.012 [async-task-pool188] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1014] - 当前页无数据，停止获取
2025-08-26 15:43:04.021 [async-task-pool188] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1083] - 拉取主机入侵攻击数据成功，但没有有效数据
2025-08-26 15:43:04.032 [async-task-pool183] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,987] - 主机入侵攻击总数: 0
2025-08-26 15:43:04.041 [async-task-pool183] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1010] - 第1页获取0条数据
2025-08-26 15:43:04.042 [async-task-pool183] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1014] - 当前页无数据，停止获取
2025-08-26 15:43:04.046 [async-task-pool183] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1083] - 拉取主机入侵攻击数据成功，但没有有效数据
2025-08-26 15:43:04.086 [pool-6-thread-5] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 23秒687毫秒
2025-08-26 15:43:04.100 [taskScheduler-14] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 299毫秒
2025-08-26 15:43:04.102 [Thread-8] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 301毫秒
2025-08-26 15:43:04.115 [Thread-6] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 314毫秒
2025-08-26 15:43:04.214 [Thread-7] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:43:04.215 [Thread-7] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：0,等待任务: 0
2025-08-26 15:43:04.215 [async-task-pool192] INFO  c.r.f.a.e.PullFlowRiskAssetsEvent - [lambda$1,100] - 开始获取流量风险资产数据: 测试非凡设备1
2025-08-26 15:43:04.215 [async-task-pool198] INFO  c.r.f.a.e.PullFlowRiskAssetsEvent - [lambda$1,100] - 开始获取流量风险资产数据: 测试设备2
2025-08-26 15:43:04.216 [async-task-pool192] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,352] - 开始拉取流量风险资产数据，参数：FlowRiskAssetsParam(accessToken=null, riskType=all, startTime=2025-08-25 15:40:59, endTime=2025-08-26 15:41:04, page=1, pageSize=30, deviceConfigId=1)
2025-08-26 15:43:04.216 [async-task-pool198] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,352] - 开始拉取流量风险资产数据，参数：FlowRiskAssetsParam(accessToken=null, riskType=all, startTime=2025-08-25 15:40:59, endTime=2025-08-26 15:41:04, page=1, pageSize=30, deviceConfigId=4)
2025-08-26 15:43:04.218 [async-task-pool192] INFO  c.r.f.a.d.FlowRiskAssetsParam - [getRequestBase,49] - 非凡API流量风险资产请求参数: https://***************:23000/v2/flow-risk-assets?access_token=lst2BAzxJTQL0eSZ&risk_type=all&start_date=2025-08-25%2015%3A40%3A59&end_date=2025-08-26%2015%3A41%3A04&page=1&page_size=30
2025-08-26 15:43:04.218 [async-task-pool198] INFO  c.r.f.a.d.FlowRiskAssetsParam - [getRequestBase,49] - 非凡API流量风险资产请求参数: https://***************:23000/v2/flow-risk-assets?access_token=lst2BAzxJTQL0eSZ&risk_type=all&start_date=2025-08-25%2015%3A40%3A59&end_date=2025-08-26%2015%3A41%3A04&page=1&page_size=30
2025-08-26 15:43:04.291 [async-task-pool192] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,458] - 流量风险资产总数: 6
2025-08-26 15:43:04.299 [async-task-pool192] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,481] - 第1页获取6条数据
2025-08-26 15:43:04.300 [async-task-pool192] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,488] - 当前页数据量(6)少于页大小(30)，已获取完所有数据
2025-08-26 15:43:04.314 [async-task-pool198] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,458] - 流量风险资产总数: 6
2025-08-26 15:43:04.322 [async-task-pool198] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,481] - 第1页获取6条数据
2025-08-26 15:43:04.322 [async-task-pool198] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,488] - 当前页数据量(6)少于页大小(30)，已获取完所有数据
2025-08-26 15:43:04.382 [async-task-pool192] INFO  c.r.m.c.c.FfsafeClientService - [processBatch,662] - 批量更新6条流量风险资产数据
2025-08-26 15:43:04.383 [async-task-pool192] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,551] - 拉取流量风险资产数据完成，耗时167ms，共1页，共6条数据，处理结果: true
2025-08-26 15:43:04.396 [async-task-pool198] INFO  c.r.m.c.c.FfsafeClientService - [processBatch,662] - 批量更新6条流量风险资产数据
2025-08-26 15:43:04.396 [async-task-pool198] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,551] - 拉取流量风险资产数据完成，耗时180ms，共1页，共6条数据，处理结果: true
2025-08-26 15:43:04.402 [Thread-7] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 188毫秒
2025-08-26 15:43:04.967 [pool-6-thread-5] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sync,255] - 批量更新攻击者视角告警列开始: 15
2025-08-26 15:43:04.984 [Thread-5] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:43:04.984 [Thread-5] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：0,等待任务: 0
2025-08-26 15:43:04.984 [async-task-pool175] INFO  c.r.f.a.e.PullDataEvent - [lambda$1,102] - 开始流量告警日志同步: 测试设备2
2025-08-26 15:43:04.984 [async-task-pool190] INFO  c.r.f.a.e.PullDataEvent - [lambda$1,102] - 开始流量告警日志同步: 测试非凡设备1
2025-08-26 15:43:05.006 [pool-6-thread-5] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=**************, operationType=UPDATE
2025-08-26 15:43:05.007 [pool-6-thread-5] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=**************, operationType=UPDATE
2025-08-26 15:43:05.013 [pool-6-thread-5] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=**************, operationType=UPDATE
2025-08-26 15:43:05.014 [pool-6-thread-5] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=************, operationType=UPDATE
2025-08-26 15:43:05.015 [pool-6-thread-5] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=***********, operationType=UPDATE
2025-08-26 15:43:05.016 [pool-6-thread-5] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=**********, operationType=UPDATE
2025-08-26 15:43:05.017 [pool-6-thread-5] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=***********, operationType=UPDATE
2025-08-26 15:43:05.019 [pool-6-thread-5] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=**********0, operationType=UPDATE
2025-08-26 15:43:05.020 [pool-6-thread-5] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=*************, operationType=UPDATE
2025-08-26 15:43:05.023 [pool-6-thread-5] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=**************, operationType=UPDATE
2025-08-26 15:43:05.024 [pool-6-thread-5] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=*************, operationType=UPDATE
2025-08-26 15:43:05.030 [pool-6-thread-5] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=************, operationType=UPDATE
2025-08-26 15:43:05.032 [pool-6-thread-5] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=***************, operationType=UPDATE
2025-08-26 15:43:05.034 [pool-6-thread-5] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=***********, operationType=UPDATE
2025-08-26 15:43:05.035 [pool-6-thread-5] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=**************, operationType=UPDATE
2025-08-26 15:43:05.039 [pool-6-thread-1] INFO  c.r.s.t.FfSafeFlowAlarmTask - [syncTask,46] - 开始同步攻击者告警数据
2025-08-26 15:43:05.098 [pool-6-thread-1] INFO  c.r.f.c.FFSafeRequestComponent - [execRequest,88] - 非凡请求响应: [{"attack_ip": "***********", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 1, "attack_type_nums": 1, "attack_nums": 259, "start_time": "2025-08-26 09:54:16", "update_time": "2025-08-26 15:41:44"}, {"attack_ip": "**********", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 1, "attack_type_nums": 1, "attack_nums": 404, "start_time": "2025-08-25 08:14:50", "update_time": "2025-08-26 15:41:42"}, {"attack_ip": "***********", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 1, "attack_type_nums": 1, "attack_nums": 279, "start_time": "2025-08-25 08:27:55", "update_time": "2025-08-26 15:41:42"}, {"attack_ip": "**********0", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 1, "attack_type_nums": 1, "attack_nums": 586, "start_time": "2025-08-25 08:59:16", "update_time": "2025-08-26 15:41:31"}, {"attack_ip": "*************", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 2, "attack_type_nums": 1, "attack_nums": 333, "start_time": "2025-08-25 08:03:53", "update_time": "2025-08-26 15:39:58"}, {"attack_ip": "*************", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 2, "attack_type_nums": 1, "attack_nums": 336, "start_time": "2025-08-25 08:02:29", "update_time": "2025-08-26 15:38:37"}, {"attack_ip": "************", "risk_level": 3, "ip_tags": ["内网恶意攻击"], "location": "局域网", "victim_ip_nums": 5, "attack_type_nums": 6, "attack_nums": 97, "start_time": "2025-08-25 16:17:50", "update_time": "2025-08-26 15:37:44"}, {"attack_ip": "***************", "risk_level": 3, "ip_tags": ["内网端口扫描", "内网恶意攻击", "暴力破解"], "location": "局域网", "victim_ip_nums": 30, "attack_type_nums": 1079, "attack_nums": 58440, "start_time": "2025-08-26 14:58:22", "update_time": "2025-08-26 15:34:10"}, {"attack_ip": "***********", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 2, "attack_type_nums": 2, "attack_nums": 155, "start_time": "2025-08-25 13:27:18", "update_time": "2025-08-26 15:33:16"}, {"attack_ip": "**************", "risk_level": 3, "ip_tags": ["内网端口扫描", "内网恶意攻击", "暴力破解"], "location": "局域网", "victim_ip_nums": 257, "attack_type_nums": 62, "attack_nums": 1176, "start_time": "2025-08-26 14:24:32", "update_time": "2025-08-26 15:32:50"}]

2025-08-26 15:43:05.107 [pool-6-thread-1] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:10...
2025-08-26 15:43:05.112 [pool-6-thread-1] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：2,等待任务: 0
2025-08-26 15:43:05.481 [Thread-5] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 497毫秒
2025-08-26 15:43:11.109 [pool-4-thread-1] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [lambda$0,81] - 最大连接：200 当前连接: 2
2025-08-26 15:43:14.107 [Thread-8] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:43:14.107 [taskScheduler-14] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:43:14.107 [Thread-8] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：1,等待任务: 0
2025-08-26 15:43:14.108 [taskScheduler-14] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：1,等待任务: 0
2025-08-26 15:43:14.108 [async-task-pool3] INFO  c.r.f.a.e.PullHostIntrusionAttackEvent - [lambda$1,83] - 开始获取主机入侵攻击数据: 测试设备2
2025-08-26 15:43:14.108 [async-task-pool191] INFO  c.r.f.a.e.PullHostIntrusionAttackEvent - [lambda$1,83] - 开始获取主机入侵攻击数据: 测试非凡设备1
2025-08-26 15:43:14.109 [async-task-pool3] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,874] - 开始拉取主机入侵攻击数据，参数：HostIntrusionAttackParam(accessToken=null, sip=null, dip=null, startDate=2025-08-25 15:41:03, endDate=2025-08-26 15:41:14, page=1, pSize=20, deviceConfigId=4)
2025-08-26 15:43:14.109 [async-task-pool191] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,874] - 开始拉取主机入侵攻击数据，参数：HostIntrusionAttackParam(accessToken=null, sip=null, dip=null, startDate=2025-08-25 15:41:03, endDate=2025-08-26 15:41:14, page=1, pSize=20, deviceConfigId=1)
2025-08-26 15:43:14.110 [async-task-pool3] INFO  c.r.f.a.d.HostIntrusionAttackParam - [getRequestBase,80] - 非凡API主机入侵攻击分页请求参数: https://***************:23000/v2/host-edr-intrusion?access_token=lst2BAzxJTQL0eSZ&start_date=2025-08-25%2015%3A41%3A03&end_date=2025-08-26%2015%3A41%3A14&page=1&p_size=20
2025-08-26 15:43:14.110 [async-task-pool191] INFO  c.r.f.a.d.HostIntrusionAttackParam - [getRequestBase,80] - 非凡API主机入侵攻击分页请求参数: https://***************:23000/v2/host-edr-intrusion?access_token=lst2BAzxJTQL0eSZ&start_date=2025-08-25%2015%3A41%3A03&end_date=2025-08-26%2015%3A41%3A14&page=1&p_size=20
2025-08-26 15:43:14.146 [Thread-6] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:43:14.146 [Thread-6] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：5,等待任务: 0
2025-08-26 15:43:14.146 [async-task-pool193] INFO  c.r.f.a.e.PullFilterLogEvent - [lambda$1,90] - 开始获取IP阻断日志: 测试非凡设备1
2025-08-26 15:43:14.146 [async-task-pool155] INFO  c.r.f.a.e.PullFilterLogEvent - [lambda$1,90] - 开始获取IP阻断日志: 测试设备2
2025-08-26 15:43:14.159 [async-task-pool184] INFO  c.r.f.c.FFSafeRequestComponent - [execRequest,88] - 非凡请求响应: [{"id": 1058, "block_ip": "*************", "location": ["美国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-22 00:58:57", "release_time": "2025-08-29 01:00:59", "remarks": ""}, {"id": 1057, "block_ip": "************", "location": ["法国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-21 13:55:52", "release_time": "2025-08-28 13:56:53", "remarks": ""}, {"id": 1056, "block_ip": "***********", "location": ["印度"], "action": "阻断", "conditions": ["外网恶意攻击"], "type": "流量检测", "create_time": "2025-08-20 12:44:55", "release_time": "2025-08-27 12:46:57", "remarks": ""}]

2025-08-26 15:43:14.159 [async-task-pool1] INFO  c.r.f.c.FFSafeRequestComponent - [execRequest,88] - 非凡请求响应: [{"id": 1058, "block_ip": "*************", "location": ["美国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-22 00:58:57", "release_time": "2025-08-29 01:00:59", "remarks": ""}, {"id": 1057, "block_ip": "************", "location": ["法国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-21 13:55:52", "release_time": "2025-08-28 13:56:53", "remarks": ""}, {"id": 1056, "block_ip": "***********", "location": ["印度"], "action": "阻断", "conditions": ["外网恶意攻击"], "type": "流量检测", "create_time": "2025-08-20 12:44:55", "release_time": "2025-08-27 12:46:57", "remarks": ""}]

2025-08-26 15:43:14.172 [async-task-pool3] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,987] - 主机入侵攻击总数: 0
2025-08-26 15:43:14.174 [async-task-pool191] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,987] - 主机入侵攻击总数: 0
2025-08-26 15:43:14.174 [async-task-pool3] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1010] - 第1页获取0条数据
2025-08-26 15:43:14.176 [async-task-pool191] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1010] - 第1页获取0条数据
2025-08-26 15:43:14.176 [async-task-pool3] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1014] - 当前页无数据，停止获取
2025-08-26 15:43:14.177 [async-task-pool191] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1014] - 当前页无数据，停止获取
2025-08-26 15:43:14.177 [async-task-pool3] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1083] - 拉取主机入侵攻击数据成功，但没有有效数据
2025-08-26 15:43:14.177 [async-task-pool191] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1083] - 拉取主机入侵攻击数据成功，但没有有效数据
2025-08-26 15:43:14.189 [taskScheduler-14] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 82毫秒
2025-08-26 15:43:14.208 [Thread-8] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 101毫秒
2025-08-26 15:43:14.219 [Thread-6] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 73毫秒
2025-08-26 15:43:14.407 [Thread-7] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:43:14.408 [Thread-7] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：1,等待任务: 0
2025-08-26 15:43:14.408 [async-task-pool11] INFO  c.r.f.a.e.PullFlowRiskAssetsEvent - [lambda$1,100] - 开始获取流量风险资产数据: 测试非凡设备1
2025-08-26 15:43:14.408 [async-task-pool179] INFO  c.r.f.a.e.PullFlowRiskAssetsEvent - [lambda$1,100] - 开始获取流量风险资产数据: 测试设备2
2025-08-26 15:43:14.408 [async-task-pool11] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,352] - 开始拉取流量风险资产数据，参数：FlowRiskAssetsParam(accessToken=null, riskType=all, startTime=2025-08-25 15:41:09, endTime=2025-08-26 15:41:14, page=1, pageSize=30, deviceConfigId=1)
2025-08-26 15:43:14.409 [async-task-pool179] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,352] - 开始拉取流量风险资产数据，参数：FlowRiskAssetsParam(accessToken=null, riskType=all, startTime=2025-08-25 15:41:09, endTime=2025-08-26 15:41:14, page=1, pageSize=30, deviceConfigId=4)
2025-08-26 15:43:14.410 [async-task-pool11] INFO  c.r.f.a.d.FlowRiskAssetsParam - [getRequestBase,49] - 非凡API流量风险资产请求参数: https://***************:23000/v2/flow-risk-assets?access_token=lst2BAzxJTQL0eSZ&risk_type=all&start_date=2025-08-25%2015%3A41%3A09&end_date=2025-08-26%2015%3A41%3A14&page=1&page_size=30
2025-08-26 15:43:14.410 [async-task-pool179] INFO  c.r.f.a.d.FlowRiskAssetsParam - [getRequestBase,49] - 非凡API流量风险资产请求参数: https://***************:23000/v2/flow-risk-assets?access_token=lst2BAzxJTQL0eSZ&risk_type=all&start_date=2025-08-25%2015%3A41%3A09&end_date=2025-08-26%2015%3A41%3A14&page=1&page_size=30
2025-08-26 15:43:14.476 [async-task-pool11] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,458] - 流量风险资产总数: 6
2025-08-26 15:43:14.477 [async-task-pool11] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,481] - 第1页获取6条数据
2025-08-26 15:43:14.477 [async-task-pool11] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,488] - 当前页数据量(6)少于页大小(30)，已获取完所有数据
2025-08-26 15:43:14.482 [async-task-pool179] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,458] - 流量风险资产总数: 6
2025-08-26 15:43:14.485 [async-task-pool179] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,481] - 第1页获取6条数据
2025-08-26 15:43:14.486 [async-task-pool179] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,488] - 当前页数据量(6)少于页大小(30)，已获取完所有数据
2025-08-26 15:43:14.500 [async-task-pool11] INFO  c.r.m.c.c.FfsafeClientService - [processBatch,662] - 批量更新6条流量风险资产数据
2025-08-26 15:43:14.503 [async-task-pool11] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,551] - 拉取流量风险资产数据完成，耗时95ms，共1页，共6条数据，处理结果: true
2025-08-26 15:43:14.518 [async-task-pool179] INFO  c.r.m.c.c.FfsafeClientService - [processBatch,662] - 批量更新6条流量风险资产数据
2025-08-26 15:43:14.519 [async-task-pool179] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,551] - 拉取流量风险资产数据完成，耗时110ms，共1页，共6条数据，处理结果: true
2025-08-26 15:43:14.524 [Thread-7] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 117毫秒
2025-08-26 15:43:15.505 [Thread-5] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:43:15.507 [Thread-5] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：1,等待任务: 0
2025-08-26 15:43:15.507 [async-task-pool9] INFO  c.r.f.a.e.PullDataEvent - [lambda$1,102] - 开始流量告警日志同步: 测试设备2
2025-08-26 15:43:15.507 [async-task-pool2] INFO  c.r.f.a.e.PullDataEvent - [lambda$1,102] - 开始流量告警日志同步: 测试非凡设备1
2025-08-26 15:43:15.946 [Thread-5] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 441毫秒
2025-08-26 15:43:18.171 [pool-6-thread-1] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 13秒64毫秒
2025-08-26 15:43:18.494 [pool-6-thread-1] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sync,255] - 批量更新攻击者视角告警列开始: 10
2025-08-26 15:43:18.527 [pool-6-thread-1] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=***********, operationType=UPDATE
2025-08-26 15:43:18.530 [pool-6-thread-1] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=**********, operationType=UPDATE
2025-08-26 15:43:18.531 [pool-6-thread-1] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=***********, operationType=UPDATE
2025-08-26 15:43:18.532 [pool-6-thread-1] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=**********0, operationType=UPDATE
2025-08-26 15:43:18.533 [pool-6-thread-1] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=*************, operationType=UPDATE
2025-08-26 15:43:18.534 [pool-6-thread-1] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=*************, operationType=UPDATE
2025-08-26 15:43:18.536 [pool-6-thread-1] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=************, operationType=UPDATE
2025-08-26 15:43:18.541 [pool-6-thread-1] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=***************, operationType=UPDATE
2025-08-26 15:43:18.544 [pool-6-thread-1] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=***********, operationType=UPDATE
2025-08-26 15:43:18.546 [pool-6-thread-1] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=**************, operationType=UPDATE
2025-08-26 15:43:18.549 [pool-6-thread-4] INFO  c.r.s.t.FfSafeFlowAlarmTask - [syncTask,46] - 开始同步攻击者告警数据
2025-08-26 15:43:18.615 [pool-6-thread-4] INFO  c.r.f.c.FFSafeRequestComponent - [execRequest,88] - 非凡请求响应: [{"attack_ip": "************", "risk_level": 3, "ip_tags": ["内网恶意攻击"], "location": "局域网", "victim_ip_nums": 9, "attack_type_nums": 36, "attack_nums": 418, "start_time": "2025-08-25 08:12:47", "update_time": "2025-08-26 15:42:03"}, {"attack_ip": "***********", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 1, "attack_type_nums": 1, "attack_nums": 259, "start_time": "2025-08-26 09:54:16", "update_time": "2025-08-26 15:41:44"}, {"attack_ip": "**********", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 1, "attack_type_nums": 1, "attack_nums": 404, "start_time": "2025-08-25 08:14:50", "update_time": "2025-08-26 15:41:42"}, {"attack_ip": "***********", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 1, "attack_type_nums": 1, "attack_nums": 279, "start_time": "2025-08-25 08:27:55", "update_time": "2025-08-26 15:41:42"}, {"attack_ip": "**********0", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 1, "attack_type_nums": 1, "attack_nums": 586, "start_time": "2025-08-25 08:59:16", "update_time": "2025-08-26 15:41:31"}, {"attack_ip": "*************", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 2, "attack_type_nums": 1, "attack_nums": 333, "start_time": "2025-08-25 08:03:53", "update_time": "2025-08-26 15:39:58"}, {"attack_ip": "*************", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 2, "attack_type_nums": 1, "attack_nums": 336, "start_time": "2025-08-25 08:02:29", "update_time": "2025-08-26 15:38:37"}, {"attack_ip": "************", "risk_level": 3, "ip_tags": ["内网恶意攻击"], "location": "局域网", "victim_ip_nums": 5, "attack_type_nums": 6, "attack_nums": 97, "start_time": "2025-08-25 16:17:50", "update_time": "2025-08-26 15:37:44"}, {"attack_ip": "***************", "risk_level": 3, "ip_tags": ["内网端口扫描", "内网恶意攻击", "暴力破解"], "location": "局域网", "victim_ip_nums": 30, "attack_type_nums": 1079, "attack_nums": 58440, "start_time": "2025-08-26 14:58:22", "update_time": "2025-08-26 15:34:10"}, {"attack_ip": "***********", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 2, "attack_type_nums": 2, "attack_nums": 155, "start_time": "2025-08-25 13:27:18", "update_time": "2025-08-26 15:33:16"}, {"attack_ip": "**************", "risk_level": 3, "ip_tags": ["内网端口扫描", "内网恶意攻击", "暴力破解"], "location": "局域网", "victim_ip_nums": 257, "attack_type_nums": 62, "attack_nums": 1176, "start_time": "2025-08-26 14:24:32", "update_time": "2025-08-26 15:32:50"}]

2025-08-26 15:43:18.624 [pool-6-thread-4] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:11...
2025-08-26 15:43:18.624 [pool-6-thread-4] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：0,等待任务: 0
2025-08-26 15:43:21.127 [pool-4-thread-1] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [lambda$0,81] - 最大连接：200 当前连接: 2
2025-08-26 15:43:24.227 [Thread-8] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:43:24.227 [taskScheduler-14] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:43:24.227 [Thread-8] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：1,等待任务: 0
2025-08-26 15:43:24.228 [taskScheduler-14] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：1,等待任务: 0
2025-08-26 15:43:24.229 [async-task-pool13] INFO  c.r.f.a.e.PullHostIntrusionAttackEvent - [lambda$1,83] - 开始获取主机入侵攻击数据: 测试非凡设备1
2025-08-26 15:43:24.229 [async-task-pool7] INFO  c.r.f.a.e.PullHostIntrusionAttackEvent - [lambda$1,83] - 开始获取主机入侵攻击数据: 测试设备2
2025-08-26 15:43:24.233 [async-task-pool13] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,874] - 开始拉取主机入侵攻击数据，参数：HostIntrusionAttackParam(accessToken=null, sip=null, dip=null, startDate=2025-08-25 15:41:14, endDate=2025-08-26 15:41:24, page=1, pSize=20, deviceConfigId=1)
2025-08-26 15:43:24.233 [async-task-pool7] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,874] - 开始拉取主机入侵攻击数据，参数：HostIntrusionAttackParam(accessToken=null, sip=null, dip=null, startDate=2025-08-25 15:41:14, endDate=2025-08-26 15:41:24, page=1, pSize=20, deviceConfigId=4)
2025-08-26 15:43:24.235 [async-task-pool13] INFO  c.r.f.a.d.HostIntrusionAttackParam - [getRequestBase,80] - 非凡API主机入侵攻击分页请求参数: https://***************:23000/v2/host-edr-intrusion?access_token=lst2BAzxJTQL0eSZ&start_date=2025-08-25%2015%3A41%3A14&end_date=2025-08-26%2015%3A41%3A24&page=1&p_size=20
2025-08-26 15:43:24.235 [async-task-pool7] INFO  c.r.f.a.d.HostIntrusionAttackParam - [getRequestBase,80] - 非凡API主机入侵攻击分页请求参数: https://***************:23000/v2/host-edr-intrusion?access_token=lst2BAzxJTQL0eSZ&start_date=2025-08-25%2015%3A41%3A14&end_date=2025-08-26%2015%3A41%3A24&page=1&p_size=20
2025-08-26 15:43:24.254 [Thread-6] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:43:24.261 [Thread-6] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：5,等待任务: 0
2025-08-26 15:43:24.265 [async-task-pool5] INFO  c.r.f.a.e.PullFilterLogEvent - [lambda$1,90] - 开始获取IP阻断日志: 测试非凡设备1
2025-08-26 15:43:24.265 [async-task-pool28] INFO  c.r.f.a.e.PullFilterLogEvent - [lambda$1,90] - 开始获取IP阻断日志: 测试设备2
2025-08-26 15:43:24.385 [async-task-pool186] INFO  c.r.f.c.FFSafeRequestComponent - [execRequest,88] - 非凡请求响应: [{"id": 1058, "block_ip": "*************", "location": ["美国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-22 00:58:57", "release_time": "2025-08-29 01:00:59", "remarks": ""}, {"id": 1057, "block_ip": "************", "location": ["法国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-21 13:55:52", "release_time": "2025-08-28 13:56:53", "remarks": ""}, {"id": 1056, "block_ip": "***********", "location": ["印度"], "action": "阻断", "conditions": ["外网恶意攻击"], "type": "流量检测", "create_time": "2025-08-20 12:44:55", "release_time": "2025-08-27 12:46:57", "remarks": ""}]

2025-08-26 15:43:24.402 [async-task-pool169] INFO  c.r.f.c.FFSafeRequestComponent - [execRequest,88] - 非凡请求响应: [{"id": 1058, "block_ip": "*************", "location": ["美国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-22 00:58:57", "release_time": "2025-08-29 01:00:59", "remarks": ""}, {"id": 1057, "block_ip": "************", "location": ["法国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-21 13:55:52", "release_time": "2025-08-28 13:56:53", "remarks": ""}, {"id": 1056, "block_ip": "***********", "location": ["印度"], "action": "阻断", "conditions": ["外网恶意攻击"], "type": "流量检测", "create_time": "2025-08-20 12:44:55", "release_time": "2025-08-27 12:46:57", "remarks": ""}]

2025-08-26 15:43:24.416 [async-task-pool13] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,987] - 主机入侵攻击总数: 0
2025-08-26 15:43:24.417 [async-task-pool7] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,987] - 主机入侵攻击总数: 0
2025-08-26 15:43:24.418 [async-task-pool13] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1010] - 第1页获取0条数据
2025-08-26 15:43:24.418 [async-task-pool7] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1010] - 第1页获取0条数据
2025-08-26 15:43:24.419 [async-task-pool13] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1014] - 当前页无数据，停止获取
2025-08-26 15:43:24.419 [async-task-pool7] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1014] - 当前页无数据，停止获取
2025-08-26 15:43:24.419 [async-task-pool13] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1083] - 拉取主机入侵攻击数据成功，但没有有效数据
2025-08-26 15:43:24.420 [async-task-pool7] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1083] - 拉取主机入侵攻击数据成功，但没有有效数据
2025-08-26 15:43:24.434 [taskScheduler-14] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 207毫秒
2025-08-26 15:43:24.435 [Thread-8] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 208毫秒
2025-08-26 15:43:24.435 [Thread-6] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 181毫秒
2025-08-26 15:43:24.536 [Thread-7] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:43:24.536 [Thread-7] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：1,等待任务: 0
2025-08-26 15:43:24.537 [async-task-pool26] INFO  c.r.f.a.e.PullFlowRiskAssetsEvent - [lambda$1,100] - 开始获取流量风险资产数据: 测试非凡设备1
2025-08-26 15:43:24.537 [async-task-pool4] INFO  c.r.f.a.e.PullFlowRiskAssetsEvent - [lambda$1,100] - 开始获取流量风险资产数据: 测试设备2
2025-08-26 15:43:24.537 [async-task-pool26] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,352] - 开始拉取流量风险资产数据，参数：FlowRiskAssetsParam(accessToken=null, riskType=all, startTime=2025-08-25 15:41:19, endTime=2025-08-26 15:41:24, page=1, pageSize=30, deviceConfigId=1)
2025-08-26 15:43:24.537 [async-task-pool4] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,352] - 开始拉取流量风险资产数据，参数：FlowRiskAssetsParam(accessToken=null, riskType=all, startTime=2025-08-25 15:41:19, endTime=2025-08-26 15:41:24, page=1, pageSize=30, deviceConfigId=4)
2025-08-26 15:43:24.538 [async-task-pool26] INFO  c.r.f.a.d.FlowRiskAssetsParam - [getRequestBase,49] - 非凡API流量风险资产请求参数: https://***************:23000/v2/flow-risk-assets?access_token=lst2BAzxJTQL0eSZ&risk_type=all&start_date=2025-08-25%2015%3A41%3A19&end_date=2025-08-26%2015%3A41%3A24&page=1&page_size=30
2025-08-26 15:43:24.539 [async-task-pool4] INFO  c.r.f.a.d.FlowRiskAssetsParam - [getRequestBase,49] - 非凡API流量风险资产请求参数: https://***************:23000/v2/flow-risk-assets?access_token=lst2BAzxJTQL0eSZ&risk_type=all&start_date=2025-08-25%2015%3A41%3A19&end_date=2025-08-26%2015%3A41%3A24&page=1&page_size=30
2025-08-26 15:43:24.612 [async-task-pool26] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,458] - 流量风险资产总数: 6
2025-08-26 15:43:24.613 [async-task-pool26] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,481] - 第1页获取6条数据
2025-08-26 15:43:24.613 [async-task-pool26] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,488] - 当前页数据量(6)少于页大小(30)，已获取完所有数据
2025-08-26 15:43:24.621 [async-task-pool4] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,458] - 流量风险资产总数: 6
2025-08-26 15:43:24.621 [async-task-pool4] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,481] - 第1页获取6条数据
2025-08-26 15:43:24.628 [async-task-pool4] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,488] - 当前页数据量(6)少于页大小(30)，已获取完所有数据
2025-08-26 15:43:24.631 [async-task-pool26] INFO  c.r.m.c.c.FfsafeClientService - [processBatch,662] - 批量更新6条流量风险资产数据
2025-08-26 15:43:24.631 [async-task-pool26] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,551] - 拉取流量风险资产数据完成，耗时94ms，共1页，共6条数据，处理结果: true
2025-08-26 15:43:24.646 [async-task-pool4] INFO  c.r.m.c.c.FfsafeClientService - [processBatch,662] - 批量更新6条流量风险资产数据
2025-08-26 15:43:24.648 [async-task-pool4] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,551] - 拉取流量风险资产数据完成，耗时111ms，共1页，共6条数据，处理结果: true
2025-08-26 15:43:24.653 [Thread-7] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 117毫秒
2025-08-26 15:43:25.961 [Thread-5] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:43:25.961 [Thread-5] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：1,等待任务: 0
2025-08-26 15:43:25.961 [async-task-pool12] INFO  c.r.f.a.e.PullDataEvent - [lambda$1,102] - 开始流量告警日志同步: 测试非凡设备1
2025-08-26 15:43:25.962 [async-task-pool16] INFO  c.r.f.a.e.PullDataEvent - [lambda$1,102] - 开始流量告警日志同步: 测试设备2
2025-08-26 15:43:26.122 [Thread-5] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 161毫秒
2025-08-26 15:43:31.267 [pool-4-thread-1] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [lambda$0,81] - 最大连接：200 当前连接: 2
2025-08-26 15:43:31.589 [pool-6-thread-4] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 12秒965毫秒
2025-08-26 15:43:31.859 [pool-6-thread-4] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sync,255] - 批量更新攻击者视角告警列开始: 11
2025-08-26 15:43:31.896 [pool-6-thread-4] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=************, operationType=UPDATE
2025-08-26 15:43:31.902 [pool-6-thread-4] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=***********, operationType=UPDATE
2025-08-26 15:43:31.905 [pool-6-thread-4] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=**********, operationType=UPDATE
2025-08-26 15:43:31.910 [pool-6-thread-4] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=***********, operationType=UPDATE
2025-08-26 15:43:31.911 [pool-6-thread-4] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=**********0, operationType=UPDATE
2025-08-26 15:43:31.913 [pool-6-thread-4] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=*************, operationType=UPDATE
2025-08-26 15:43:31.918 [pool-6-thread-4] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=*************, operationType=UPDATE
2025-08-26 15:43:31.920 [pool-6-thread-4] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=************, operationType=UPDATE
2025-08-26 15:43:31.921 [pool-6-thread-4] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=***************, operationType=UPDATE
2025-08-26 15:43:31.925 [pool-6-thread-4] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=***********, operationType=UPDATE
2025-08-26 15:43:31.927 [pool-6-thread-4] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=**************, operationType=UPDATE
2025-08-26 15:43:31.933 [pool-6-thread-3] INFO  c.r.s.t.FfSafeFlowAlarmTask - [syncTask,46] - 开始同步攻击者告警数据
2025-08-26 15:43:31.994 [pool-6-thread-3] INFO  c.r.f.c.FFSafeRequestComponent - [execRequest,88] - 非凡请求响应: [{"attack_ip": "***********", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 1, "attack_type_nums": 1, "attack_nums": 259, "start_time": "2025-08-26 09:54:16", "update_time": "2025-08-26 15:41:44"}, {"attack_ip": "**********", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 1, "attack_type_nums": 1, "attack_nums": 404, "start_time": "2025-08-25 08:14:50", "update_time": "2025-08-26 15:41:42"}, {"attack_ip": "***********", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 1, "attack_type_nums": 1, "attack_nums": 279, "start_time": "2025-08-25 08:27:55", "update_time": "2025-08-26 15:41:42"}, {"attack_ip": "*************", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 2, "attack_type_nums": 1, "attack_nums": 333, "start_time": "2025-08-25 08:03:53", "update_time": "2025-08-26 15:39:58"}, {"attack_ip": "*************", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 2, "attack_type_nums": 1, "attack_nums": 336, "start_time": "2025-08-25 08:02:29", "update_time": "2025-08-26 15:38:37"}, {"attack_ip": "************", "risk_level": 3, "ip_tags": ["内网恶意攻击"], "location": "局域网", "victim_ip_nums": 5, "attack_type_nums": 6, "attack_nums": 97, "start_time": "2025-08-25 16:17:50", "update_time": "2025-08-26 15:37:44"}, {"attack_ip": "***************", "risk_level": 3, "ip_tags": ["内网端口扫描", "内网恶意攻击", "暴力破解"], "location": "局域网", "victim_ip_nums": 30, "attack_type_nums": 1079, "attack_nums": 58440, "start_time": "2025-08-26 14:58:22", "update_time": "2025-08-26 15:34:10"}, {"attack_ip": "***********", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 2, "attack_type_nums": 2, "attack_nums": 155, "start_time": "2025-08-25 13:27:18", "update_time": "2025-08-26 15:33:16"}, {"attack_ip": "**************", "risk_level": 3, "ip_tags": ["内网端口扫描", "内网恶意攻击", "暴力破解"], "location": "局域网", "victim_ip_nums": 257, "attack_type_nums": 62, "attack_nums": 1176, "start_time": "2025-08-26 14:24:32", "update_time": "2025-08-26 15:32:50"}]

2025-08-26 15:43:32.004 [pool-6-thread-3] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:9...
2025-08-26 15:43:32.005 [pool-6-thread-3] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：0,等待任务: 0
2025-08-26 15:43:34.442 [Thread-8] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:43:34.443 [Thread-8] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：1,等待任务: 0
2025-08-26 15:43:34.443 [taskScheduler-14] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:43:34.444 [Thread-6] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:43:34.444 [async-task-pool48] INFO  c.r.f.a.e.PullHostIntrusionAttackEvent - [lambda$1,83] - 开始获取主机入侵攻击数据: 测试非凡设备1
2025-08-26 15:43:34.444 [async-task-pool21] INFO  c.r.f.a.e.PullHostIntrusionAttackEvent - [lambda$1,83] - 开始获取主机入侵攻击数据: 测试设备2
2025-08-26 15:43:34.444 [taskScheduler-14] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：3,等待任务: 0
2025-08-26 15:43:34.445 [Thread-6] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：3,等待任务: 0
2025-08-26 15:43:34.445 [async-task-pool48] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,874] - 开始拉取主机入侵攻击数据，参数：HostIntrusionAttackParam(accessToken=null, sip=null, dip=null, startDate=2025-08-25 15:41:24, endDate=2025-08-26 15:41:34, page=1, pSize=20, deviceConfigId=1)
2025-08-26 15:43:34.446 [async-task-pool21] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,874] - 开始拉取主机入侵攻击数据，参数：HostIntrusionAttackParam(accessToken=null, sip=null, dip=null, startDate=2025-08-25 15:41:24, endDate=2025-08-26 15:41:34, page=1, pSize=20, deviceConfigId=4)
2025-08-26 15:43:34.448 [async-task-pool49] INFO  c.r.f.a.e.PullFilterLogEvent - [lambda$1,90] - 开始获取IP阻断日志: 测试非凡设备1
2025-08-26 15:43:34.451 [async-task-pool36] INFO  c.r.f.a.e.PullFilterLogEvent - [lambda$1,90] - 开始获取IP阻断日志: 测试设备2
2025-08-26 15:43:34.455 [async-task-pool48] INFO  c.r.f.a.d.HostIntrusionAttackParam - [getRequestBase,80] - 非凡API主机入侵攻击分页请求参数: https://***************:23000/v2/host-edr-intrusion?access_token=lst2BAzxJTQL0eSZ&start_date=2025-08-25%2015%3A41%3A24&end_date=2025-08-26%2015%3A41%3A34&page=1&p_size=20
2025-08-26 15:43:34.456 [async-task-pool21] INFO  c.r.f.a.d.HostIntrusionAttackParam - [getRequestBase,80] - 非凡API主机入侵攻击分页请求参数: https://***************:23000/v2/host-edr-intrusion?access_token=lst2BAzxJTQL0eSZ&start_date=2025-08-25%2015%3A41%3A24&end_date=2025-08-26%2015%3A41%3A34&page=1&p_size=20
2025-08-26 15:43:34.490 [async-task-pool32] INFO  c.r.f.c.FFSafeRequestComponent - [execRequest,88] - 非凡请求响应: [{"id": 1058, "block_ip": "*************", "location": ["美国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-22 00:58:57", "release_time": "2025-08-29 01:00:59", "remarks": ""}, {"id": 1057, "block_ip": "************", "location": ["法国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-21 13:55:52", "release_time": "2025-08-28 13:56:53", "remarks": ""}, {"id": 1056, "block_ip": "***********", "location": ["印度"], "action": "阻断", "conditions": ["外网恶意攻击"], "type": "流量检测", "create_time": "2025-08-20 12:44:55", "release_time": "2025-08-27 12:46:57", "remarks": ""}]

2025-08-26 15:43:34.509 [async-task-pool24] INFO  c.r.f.c.FFSafeRequestComponent - [execRequest,88] - 非凡请求响应: [{"id": 1058, "block_ip": "*************", "location": ["美国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-22 00:58:57", "release_time": "2025-08-29 01:00:59", "remarks": ""}, {"id": 1057, "block_ip": "************", "location": ["法国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-21 13:55:52", "release_time": "2025-08-28 13:56:53", "remarks": ""}, {"id": 1056, "block_ip": "***********", "location": ["印度"], "action": "阻断", "conditions": ["外网恶意攻击"], "type": "流量检测", "create_time": "2025-08-20 12:44:55", "release_time": "2025-08-27 12:46:57", "remarks": ""}]

2025-08-26 15:43:34.513 [async-task-pool21] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,987] - 主机入侵攻击总数: 0
2025-08-26 15:43:34.513 [async-task-pool21] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1010] - 第1页获取0条数据
2025-08-26 15:43:34.513 [async-task-pool21] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1014] - 当前页无数据，停止获取
2025-08-26 15:43:34.514 [async-task-pool21] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1083] - 拉取主机入侵攻击数据成功，但没有有效数据
2025-08-26 15:43:34.523 [taskScheduler-14] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 80毫秒
2025-08-26 15:43:34.523 [async-task-pool48] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,987] - 主机入侵攻击总数: 0
2025-08-26 15:43:34.524 [async-task-pool48] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1010] - 第1页获取0条数据
2025-08-26 15:43:34.525 [async-task-pool48] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1014] - 当前页无数据，停止获取
2025-08-26 15:43:34.526 [async-task-pool48] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1083] - 拉取主机入侵攻击数据成功，但没有有效数据
2025-08-26 15:43:34.530 [Thread-6] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 86毫秒
2025-08-26 15:43:34.539 [Thread-8] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 97毫秒
2025-08-26 15:43:34.545 [taskScheduler-21] INFO  c.r.r.s.HandleDataSyncSender - [sendDataSync,230] - 数据同步发送成功, messageId: 2370eacb-b6e4-4e37-ae07-c666eef4c156, 加密状态: 已加密, 消息类型: PING
2025-08-26 15:43:34.545 [taskScheduler-21] INFO  c.r.r.t.RabbitMQTask - [ping,44] - 发送心跳消息
2025-08-26 15:43:34.552 [rabbitConnectionFactory11] INFO  c.r.f.c.r.RabbitMQConfig - [lambda$0,290] - 消息已成功发送到交换机, messageId: 2370eacb-b6e4-4e37-ae07-c666eef4c156
2025-08-26 15:43:34.659 [Thread-7] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:43:34.659 [Thread-7] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：1,等待任务: 0
2025-08-26 15:43:34.660 [async-task-pool63] INFO  c.r.f.a.e.PullFlowRiskAssetsEvent - [lambda$1,100] - 开始获取流量风险资产数据: 测试设备2
2025-08-26 15:43:34.660 [async-task-pool29] INFO  c.r.f.a.e.PullFlowRiskAssetsEvent - [lambda$1,100] - 开始获取流量风险资产数据: 测试非凡设备1
2025-08-26 15:43:34.660 [async-task-pool63] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,352] - 开始拉取流量风险资产数据，参数：FlowRiskAssetsParam(accessToken=null, riskType=all, startTime=2025-08-25 15:41:30, endTime=2025-08-26 15:41:34, page=1, pageSize=30, deviceConfigId=4)
2025-08-26 15:43:34.660 [async-task-pool29] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,352] - 开始拉取流量风险资产数据，参数：FlowRiskAssetsParam(accessToken=null, riskType=all, startTime=2025-08-25 15:41:30, endTime=2025-08-26 15:41:34, page=1, pageSize=30, deviceConfigId=1)
2025-08-26 15:43:34.661 [async-task-pool63] INFO  c.r.f.a.d.FlowRiskAssetsParam - [getRequestBase,49] - 非凡API流量风险资产请求参数: https://***************:23000/v2/flow-risk-assets?access_token=lst2BAzxJTQL0eSZ&risk_type=all&start_date=2025-08-25%2015%3A41%3A30&end_date=2025-08-26%2015%3A41%3A34&page=1&page_size=30
2025-08-26 15:43:34.662 [async-task-pool29] INFO  c.r.f.a.d.FlowRiskAssetsParam - [getRequestBase,49] - 非凡API流量风险资产请求参数: https://***************:23000/v2/flow-risk-assets?access_token=lst2BAzxJTQL0eSZ&risk_type=all&start_date=2025-08-25%2015%3A41%3A30&end_date=2025-08-26%2015%3A41%3A34&page=1&page_size=30
2025-08-26 15:43:34.735 [async-task-pool29] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,458] - 流量风险资产总数: 6
2025-08-26 15:43:34.736 [async-task-pool29] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,481] - 第1页获取6条数据
2025-08-26 15:43:34.736 [async-task-pool29] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,488] - 当前页数据量(6)少于页大小(30)，已获取完所有数据
2025-08-26 15:43:34.738 [async-task-pool63] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,458] - 流量风险资产总数: 6
2025-08-26 15:43:34.739 [async-task-pool63] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,481] - 第1页获取6条数据
2025-08-26 15:43:34.740 [async-task-pool63] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,488] - 当前页数据量(6)少于页大小(30)，已获取完所有数据
2025-08-26 15:43:34.755 [async-task-pool29] INFO  c.r.m.c.c.FfsafeClientService - [processBatch,662] - 批量更新6条流量风险资产数据
2025-08-26 15:43:34.758 [async-task-pool29] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,551] - 拉取流量风险资产数据完成，耗时98ms，共1页，共6条数据，处理结果: true
2025-08-26 15:43:34.761 [async-task-pool63] INFO  c.r.m.c.c.FfsafeClientService - [processBatch,662] - 批量更新6条流量风险资产数据
2025-08-26 15:43:34.761 [async-task-pool63] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,551] - 拉取流量风险资产数据完成，耗时101ms，共1页，共6条数据，处理结果: true
2025-08-26 15:43:34.771 [Thread-7] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 113毫秒
2025-08-26 15:43:35.121 [taskScheduler-29] INFO  c.r.c.task.HlsTask - [checkHls,65] - hls检查开始...
2025-08-26 15:43:35.124 [taskScheduler-29] INFO  c.r.c.task.HlsTask - [checkHls,86] - hls检查结束...
2025-08-26 15:43:36.126 [Thread-5] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:43:36.126 [Thread-5] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：1,等待任务: 0
2025-08-26 15:43:36.127 [async-task-pool66] INFO  c.r.f.a.e.PullDataEvent - [lambda$1,102] - 开始流量告警日志同步: 测试非凡设备1
2025-08-26 15:43:36.127 [async-task-pool47] INFO  c.r.f.a.e.PullDataEvent - [lambda$1,102] - 开始流量告警日志同步: 测试设备2
2025-08-26 15:43:36.302 [Thread-5] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 176毫秒
2025-08-26 15:43:41.270 [pool-4-thread-1] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [lambda$0,81] - 最大连接：200 当前连接: 2
2025-08-26 15:43:44.538 [Thread-6] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:43:44.538 [taskScheduler-45] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:43:44.539 [Thread-6] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：1,等待任务: 0
2025-08-26 15:43:44.539 [taskScheduler-45] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：1,等待任务: 0
2025-08-26 15:43:44.539 [async-task-pool39] INFO  c.r.f.a.e.PullFilterLogEvent - [lambda$1,90] - 开始获取IP阻断日志: 测试设备2
2025-08-26 15:43:44.539 [async-task-pool64] INFO  c.r.f.a.e.PullFilterLogEvent - [lambda$1,90] - 开始获取IP阻断日志: 测试非凡设备1
2025-08-26 15:43:44.562 [Thread-8] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:43:44.562 [Thread-8] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：5,等待任务: 0
2025-08-26 15:43:44.563 [async-task-pool69] INFO  c.r.f.a.e.PullHostIntrusionAttackEvent - [lambda$1,83] - 开始获取主机入侵攻击数据: 测试设备2
2025-08-26 15:43:44.563 [async-task-pool67] INFO  c.r.f.a.e.PullHostIntrusionAttackEvent - [lambda$1,83] - 开始获取主机入侵攻击数据: 测试非凡设备1
2025-08-26 15:43:44.564 [async-task-pool69] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,874] - 开始拉取主机入侵攻击数据，参数：HostIntrusionAttackParam(accessToken=null, sip=null, dip=null, startDate=2025-08-25 15:41:34, endDate=2025-08-26 15:41:44, page=1, pSize=20, deviceConfigId=4)
2025-08-26 15:43:44.564 [async-task-pool67] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,874] - 开始拉取主机入侵攻击数据，参数：HostIntrusionAttackParam(accessToken=null, sip=null, dip=null, startDate=2025-08-25 15:41:34, endDate=2025-08-26 15:41:44, page=1, pSize=20, deviceConfigId=1)
2025-08-26 15:43:44.565 [async-task-pool69] INFO  c.r.f.a.d.HostIntrusionAttackParam - [getRequestBase,80] - 非凡API主机入侵攻击分页请求参数: https://***************:23000/v2/host-edr-intrusion?access_token=lst2BAzxJTQL0eSZ&start_date=2025-08-25%2015%3A41%3A34&end_date=2025-08-26%2015%3A41%3A44&page=1&p_size=20
2025-08-26 15:43:44.565 [async-task-pool67] INFO  c.r.f.a.d.HostIntrusionAttackParam - [getRequestBase,80] - 非凡API主机入侵攻击分页请求参数: https://***************:23000/v2/host-edr-intrusion?access_token=lst2BAzxJTQL0eSZ&start_date=2025-08-25%2015%3A41%3A34&end_date=2025-08-26%2015%3A41%3A44&page=1&p_size=20
2025-08-26 15:43:44.634 [async-task-pool25] INFO  c.r.f.c.FFSafeRequestComponent - [execRequest,88] - 非凡请求响应: [{"id": 1058, "block_ip": "*************", "location": ["美国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-22 00:58:57", "release_time": "2025-08-29 01:00:59", "remarks": ""}, {"id": 1057, "block_ip": "************", "location": ["法国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-21 13:55:52", "release_time": "2025-08-28 13:56:53", "remarks": ""}, {"id": 1056, "block_ip": "***********", "location": ["印度"], "action": "阻断", "conditions": ["外网恶意攻击"], "type": "流量检测", "create_time": "2025-08-20 12:44:55", "release_time": "2025-08-27 12:46:57", "remarks": ""}]

2025-08-26 15:43:44.636 [async-task-pool62] INFO  c.r.f.c.FFSafeRequestComponent - [execRequest,88] - 非凡请求响应: [{"id": 1058, "block_ip": "*************", "location": ["美国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-22 00:58:57", "release_time": "2025-08-29 01:00:59", "remarks": ""}, {"id": 1057, "block_ip": "************", "location": ["法国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-21 13:55:52", "release_time": "2025-08-28 13:56:53", "remarks": ""}, {"id": 1056, "block_ip": "***********", "location": ["印度"], "action": "阻断", "conditions": ["外网恶意攻击"], "type": "流量检测", "create_time": "2025-08-20 12:44:55", "release_time": "2025-08-27 12:46:57", "remarks": ""}]

2025-08-26 15:43:44.647 [async-task-pool69] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,987] - 主机入侵攻击总数: 0
2025-08-26 15:43:44.647 [async-task-pool67] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,987] - 主机入侵攻击总数: 0
2025-08-26 15:43:44.648 [async-task-pool69] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1010] - 第1页获取0条数据
2025-08-26 15:43:44.648 [async-task-pool67] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1010] - 第1页获取0条数据
2025-08-26 15:43:44.649 [async-task-pool69] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1014] - 当前页无数据，停止获取
2025-08-26 15:43:44.649 [async-task-pool67] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1014] - 当前页无数据，停止获取
2025-08-26 15:43:44.650 [async-task-pool69] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1083] - 拉取主机入侵攻击数据成功，但没有有效数据
2025-08-26 15:43:44.650 [async-task-pool67] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1083] - 拉取主机入侵攻击数据成功，但没有有效数据
2025-08-26 15:43:44.659 [Thread-6] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 121毫秒
2025-08-26 15:43:44.660 [taskScheduler-45] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 122毫秒
2025-08-26 15:43:44.662 [Thread-8] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 100毫秒
2025-08-26 15:43:44.761 [pool-6-thread-3] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 12秒757毫秒
2025-08-26 15:43:44.789 [Thread-7] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:43:44.789 [Thread-7] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：0,等待任务: 0
2025-08-26 15:43:44.790 [async-task-pool35] INFO  c.r.f.a.e.PullFlowRiskAssetsEvent - [lambda$1,100] - 开始获取流量风险资产数据: 测试非凡设备1
2025-08-26 15:43:44.790 [async-task-pool65] INFO  c.r.f.a.e.PullFlowRiskAssetsEvent - [lambda$1,100] - 开始获取流量风险资产数据: 测试设备2
2025-08-26 15:43:44.792 [async-task-pool35] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,352] - 开始拉取流量风险资产数据，参数：FlowRiskAssetsParam(accessToken=null, riskType=all, startTime=2025-08-25 15:41:40, endTime=2025-08-26 15:41:44, page=1, pageSize=30, deviceConfigId=1)
2025-08-26 15:43:44.794 [async-task-pool65] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,352] - 开始拉取流量风险资产数据，参数：FlowRiskAssetsParam(accessToken=null, riskType=all, startTime=2025-08-25 15:41:40, endTime=2025-08-26 15:41:44, page=1, pageSize=30, deviceConfigId=4)
2025-08-26 15:43:44.795 [async-task-pool35] INFO  c.r.f.a.d.FlowRiskAssetsParam - [getRequestBase,49] - 非凡API流量风险资产请求参数: https://***************:23000/v2/flow-risk-assets?access_token=lst2BAzxJTQL0eSZ&risk_type=all&start_date=2025-08-25%2015%3A41%3A40&end_date=2025-08-26%2015%3A41%3A44&page=1&page_size=30
2025-08-26 15:43:44.796 [async-task-pool65] INFO  c.r.f.a.d.FlowRiskAssetsParam - [getRequestBase,49] - 非凡API流量风险资产请求参数: https://***************:23000/v2/flow-risk-assets?access_token=lst2BAzxJTQL0eSZ&risk_type=all&start_date=2025-08-25%2015%3A41%3A40&end_date=2025-08-26%2015%3A41%3A44&page=1&page_size=30
2025-08-26 15:43:44.867 [async-task-pool35] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,458] - 流量风险资产总数: 6
2025-08-26 15:43:44.868 [async-task-pool35] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,481] - 第1页获取6条数据
2025-08-26 15:43:44.868 [async-task-pool35] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,488] - 当前页数据量(6)少于页大小(30)，已获取完所有数据
2025-08-26 15:43:44.871 [async-task-pool65] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,458] - 流量风险资产总数: 6
2025-08-26 15:43:44.871 [async-task-pool65] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,481] - 第1页获取6条数据
2025-08-26 15:43:44.872 [async-task-pool65] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,488] - 当前页数据量(6)少于页大小(30)，已获取完所有数据
2025-08-26 15:43:44.896 [async-task-pool35] INFO  c.r.m.c.c.FfsafeClientService - [processBatch,662] - 批量更新6条流量风险资产数据
2025-08-26 15:43:44.899 [async-task-pool35] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,551] - 拉取流量风险资产数据完成，耗时107ms，共1页，共6条数据，处理结果: true
2025-08-26 15:43:44.902 [async-task-pool65] INFO  c.r.m.c.c.FfsafeClientService - [processBatch,662] - 批量更新6条流量风险资产数据
2025-08-26 15:43:44.903 [async-task-pool65] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,551] - 拉取流量风险资产数据完成，耗时109ms，共1页，共6条数据，处理结果: true
2025-08-26 15:43:44.914 [Thread-7] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 125毫秒
2025-08-26 15:43:45.029 [pool-6-thread-3] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sync,255] - 批量更新攻击者视角告警列开始: 9
2025-08-26 15:43:45.062 [pool-6-thread-3] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=***********, operationType=UPDATE
2025-08-26 15:43:45.063 [pool-6-thread-3] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=**********, operationType=UPDATE
2025-08-26 15:43:45.065 [pool-6-thread-3] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=***********, operationType=UPDATE
2025-08-26 15:43:45.067 [pool-6-thread-3] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=*************, operationType=UPDATE
2025-08-26 15:43:45.071 [pool-6-thread-3] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=*************, operationType=UPDATE
2025-08-26 15:43:45.079 [pool-6-thread-3] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=************, operationType=UPDATE
2025-08-26 15:43:45.095 [pool-6-thread-3] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=***************, operationType=UPDATE
2025-08-26 15:43:45.098 [pool-6-thread-3] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=***********, operationType=UPDATE
2025-08-26 15:43:45.101 [pool-6-thread-3] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=**************, operationType=UPDATE
2025-08-26 15:43:45.113 [pool-6-thread-4] INFO  c.r.s.t.FfSafeFlowAlarmTask - [syncTask,46] - 开始同步攻击者告警数据
2025-08-26 15:43:45.167 [pool-6-thread-4] INFO  c.r.f.c.FFSafeRequestComponent - [execRequest,88] - 非凡请求响应: [{"attack_ip": "**************", "risk_level": 2, "ip_tags": ["弱口令登录成功"], "location": "局域网", "victim_ip_nums": 2, "attack_type_nums": 5, "attack_nums": 7388, "start_time": "2025-08-25 08:00:02", "update_time": "2025-08-26 15:43:10"}, {"attack_ip": "**************", "risk_level": 3, "ip_tags": ["非法外联"], "location": "局域网", "victim_ip_nums": 2, "attack_type_nums": 2, "attack_nums": 176321, "start_time": "2025-08-25 08:00:04", "update_time": "2025-08-26 15:43:07"}, {"attack_ip": "**************", "risk_level": 3, "ip_tags": ["暴力破解"], "location": "局域网", "victim_ip_nums": 9, "attack_type_nums": 9, "attack_nums": 14943, "start_time": "2025-08-25 08:00:02", "update_time": "2025-08-26 15:43:05"}, {"attack_ip": "**********0", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 1, "attack_type_nums": 1, "attack_nums": 587, "start_time": "2025-08-25 08:59:16", "update_time": "2025-08-26 15:43:01"}, {"attack_ip": "**************", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 1, "attack_type_nums": 1, "attack_nums": 15, "start_time": "2025-08-26 11:06:34", "update_time": "2025-08-26 15:42:35"}, {"attack_ip": "************", "risk_level": 3, "ip_tags": ["内网恶意攻击"], "location": "局域网", "victim_ip_nums": 9, "attack_type_nums": 36, "attack_nums": 418, "start_time": "2025-08-25 08:12:47", "update_time": "2025-08-26 15:42:03"}, {"attack_ip": "***********", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 1, "attack_type_nums": 1, "attack_nums": 259, "start_time": "2025-08-26 09:54:16", "update_time": "2025-08-26 15:41:44"}, {"attack_ip": "**********", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 1, "attack_type_nums": 1, "attack_nums": 404, "start_time": "2025-08-25 08:14:50", "update_time": "2025-08-26 15:41:42"}, {"attack_ip": "***********", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 1, "attack_type_nums": 1, "attack_nums": 279, "start_time": "2025-08-25 08:27:55", "update_time": "2025-08-26 15:41:42"}, {"attack_ip": "*************", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 2, "attack_type_nums": 1, "attack_nums": 333, "start_time": "2025-08-25 08:03:53", "update_time": "2025-08-26 15:39:58"}, {"attack_ip": "*************", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 2, "attack_type_nums": 1, "attack_nums": 336, "start_time": "2025-08-25 08:02:29", "update_time": "2025-08-26 15:38:37"}, {"attack_ip": "************", "risk_level": 3, "ip_tags": ["内网恶意攻击"], "location": "局域网", "victim_ip_nums": 5, "attack_type_nums": 6, "attack_nums": 97, "start_time": "2025-08-25 16:17:50", "update_time": "2025-08-26 15:37:44"}, {"attack_ip": "***************", "risk_level": 3, "ip_tags": ["内网端口扫描", "内网恶意攻击", "暴力破解"], "location": "局域网", "victim_ip_nums": 30, "attack_type_nums": 1079, "attack_nums": 58440, "start_time": "2025-08-26 14:58:22", "update_time": "2025-08-26 15:34:10"}, {"attack_ip": "***********", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 2, "attack_type_nums": 2, "attack_nums": 155, "start_time": "2025-08-25 13:27:18", "update_time": "2025-08-26 15:33:16"}]

2025-08-26 15:43:45.202 [pool-6-thread-4] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:14...
2025-08-26 15:43:45.204 [pool-6-thread-4] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：0,等待任务: 0
2025-08-26 15:43:46.315 [Thread-5] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:43:46.315 [Thread-5] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：4,等待任务: 0
2025-08-26 15:43:46.316 [async-task-pool102] INFO  c.r.f.a.e.PullDataEvent - [lambda$1,102] - 开始流量告警日志同步: 测试非凡设备1
2025-08-26 15:43:46.316 [async-task-pool41] INFO  c.r.f.a.e.PullDataEvent - [lambda$1,102] - 开始流量告警日志同步: 测试设备2
2025-08-26 15:43:46.791 [Thread-5] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 476毫秒
2025-08-26 15:43:49.069 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-08-26 15:43:50.488 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-08-26 15:43:50.489 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-08-26 15:43:50.493 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-08-26 15:43:50.494 [SpringApplicationShutdownHook] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
2025-08-26 15:43:50.564 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2051] - {dataSource-1} closing ...
2025-08-26 15:43:50.619 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2124] - {dataSource-1} closed
