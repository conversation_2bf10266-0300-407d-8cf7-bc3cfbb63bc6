2025-08-26 15:38:55.925 [pool-6-thread-3] INFO  c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,513] - 发送攻击告警同步消息成功: attackIp=**************, operationType=UPDATE
2025-08-26 15:38:56.050 [pool-6-thread-4] INFO  c.r.s.t.FfSafeFlowAlarmTask - [syncTask,46] - 开始同步攻击者告警数据
2025-08-26 15:38:56.611 [pool-6-thread-4] INFO  c.r.f.c.FFSafeRequestComponent - [execRequest,88] - 非凡请求响应: [{"attack_ip": "************", "risk_level": 3, "ip_tags": ["内网恶意攻击"], "location": "局域网", "victim_ip_nums": 5, "attack_type_nums": 6, "attack_nums": 97, "start_time": "2025-08-25 16:17:50", "update_time": "2025-08-26 15:37:44"}, {"attack_ip": "***********", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 1, "attack_type_nums": 1, "attack_nums": 239, "start_time": "2025-08-26 09:54:16", "update_time": "2025-08-26 15:37:42"}, {"attack_ip": "***************", "risk_level": 3, "ip_tags": ["内网端口扫描", "内网恶意攻击", "暴力破解"], "location": "局域网", "victim_ip_nums": 30, "attack_type_nums": 1079, "attack_nums": 58440, "start_time": "2025-08-26 14:58:22", "update_time": "2025-08-26 15:34:10"}, {"attack_ip": "***********", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 2, "attack_type_nums": 2, "attack_nums": 155, "start_time": "2025-08-25 13:27:18", "update_time": "2025-08-26 15:33:16"}, {"attack_ip": "**************", "risk_level": 3, "ip_tags": ["内网端口扫描", "内网恶意攻击", "暴力破解"], "location": "局域网", "victim_ip_nums": 257, "attack_type_nums": 62, "attack_nums": 1176, "start_time": "2025-08-26 14:24:32", "update_time": "2025-08-26 15:32:50"}, {"attack_ip": "***********", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 1, "attack_type_nums": 3, "attack_nums": 106, "start_time": "2025-08-26 09:49:05", "update_time": "2025-08-26 15:31:50"}, {"attack_ip": "***********", "risk_level": 2, "ip_tags": ["踩中蜜罐", "内网可疑行为"], "location": "局域网", "victim_ip_nums": 2, "attack_type_nums": 6, "attack_nums": 115, "start_time": "2025-08-25 14:42:32", "update_time": "2025-08-26 15:30:35"}, {"attack_ip": "**********", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 1, "attack_type_nums": 1, "attack_nums": 403, "start_time": "2025-08-25 08:14:50", "update_time": "2025-08-26 15:29:54"}, {"attack_ip": "***********", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 1, "attack_type_nums": 1, "attack_nums": 277, "start_time": "2025-08-25 08:27:55", "update_time": "2025-08-26 15:29:41"}, {"attack_ip": "**************", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 1, "attack_type_nums": 1, "attack_nums": 13, "start_time": "2025-08-26 11:06:34", "update_time": "2025-08-26 15:28:37"}, {"attack_ip": "*************", "risk_level": 1, "ip_tags": [], "location": "局域网", "victim_ip_nums": 2, "attack_type_nums": 1, "attack_nums": 331, "start_time": "2025-08-25 08:03:53", "update_time": "2025-08-26 15:27:58"}]

2025-08-26 15:38:56.778 [pool-6-thread-4] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:11...
2025-08-26 15:38:56.943 [pool-6-thread-4] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：0,等待任务: 0
2025-08-26 15:38:57.232 [Thread-8] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:38:57.233 [Thread-6] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:38:57.340 [Thread-8] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：11,等待任务: 0
2025-08-26 15:38:57.341 [Thread-6] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：11,等待任务: 0
2025-08-26 15:38:57.352 [async-task-pool187] INFO  c.r.f.a.e.PullHostIntrusionAttackEvent - [lambda$1,83] - 开始获取主机入侵攻击数据: 测试设备2
2025-08-26 15:38:57.361 [async-task-pool187] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,874] - 开始拉取主机入侵攻击数据，参数：HostIntrusionAttackParam(accessToken=null, sip=null, dip=null, startDate=2025-08-25 15:36:46, endDate=2025-08-26 15:36:57, page=1, pSize=20, deviceConfigId=4)
2025-08-26 15:38:57.363 [async-task-pool187] INFO  c.r.f.a.d.HostIntrusionAttackParam - [getRequestBase,80] - 非凡API主机入侵攻击分页请求参数: https://***************:23000/v2/host-edr-intrusion?access_token=lst2BAzxJTQL0eSZ&start_date=2025-08-25%2015%3A36%3A46&end_date=2025-08-26%2015%3A36%3A57&page=1&p_size=20
2025-08-26 15:38:57.352 [async-task-pool9] INFO  c.r.f.a.e.PullHostIntrusionAttackEvent - [lambda$1,83] - 开始获取主机入侵攻击数据: 测试非凡设备1
2025-08-26 15:38:57.364 [async-task-pool9] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,874] - 开始拉取主机入侵攻击数据，参数：HostIntrusionAttackParam(accessToken=null, sip=null, dip=null, startDate=2025-08-25 15:36:46, endDate=2025-08-26 15:36:57, page=1, pSize=20, deviceConfigId=1)
2025-08-26 15:38:57.367 [async-task-pool9] INFO  c.r.f.a.d.HostIntrusionAttackParam - [getRequestBase,80] - 非凡API主机入侵攻击分页请求参数: https://***************:23000/v2/host-edr-intrusion?access_token=lst2BAzxJTQL0eSZ&start_date=2025-08-25%2015%3A36%3A46&end_date=2025-08-26%2015%3A36%3A57&page=1&p_size=20
2025-08-26 15:38:57.352 [async-task-pool179] INFO  c.r.f.a.e.PullFilterLogEvent - [lambda$1,90] - 开始获取IP阻断日志: 测试非凡设备1
2025-08-26 15:38:57.352 [async-task-pool165] INFO  c.r.f.a.e.PullFilterLogEvent - [lambda$1,90] - 开始获取IP阻断日志: 测试设备2
2025-08-26 15:38:58.101 [taskScheduler-31] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:38:58.104 [taskScheduler-31] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：12,等待任务: 0
2025-08-26 15:38:58.221 [async-task-pool155] INFO  c.r.f.c.FFSafeRequestComponent - [execRequest,88] - 非凡请求响应: [{"id": 1058, "block_ip": "*************", "location": ["美国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-22 00:58:57", "release_time": "2025-08-29 01:00:59", "remarks": ""}, {"id": 1057, "block_ip": "************", "location": ["法国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-21 13:55:52", "release_time": "2025-08-28 13:56:53", "remarks": ""}, {"id": 1056, "block_ip": "***********", "location": ["印度"], "action": "阻断", "conditions": ["外网恶意攻击"], "type": "流量检测", "create_time": "2025-08-20 12:44:55", "release_time": "2025-08-27 12:46:57", "remarks": ""}]

2025-08-26 15:38:58.237 [Thread-7] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-26 15:38:58.242 [Thread-7] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：8,等待任务: 0
2025-08-26 15:38:58.243 [async-task-pool15] INFO  c.r.f.a.e.PullFlowRiskAssetsEvent - [lambda$1,100] - 开始获取流量风险资产数据: 测试设备2
2025-08-26 15:38:58.243 [async-task-pool18] INFO  c.r.f.a.e.PullFlowRiskAssetsEvent - [lambda$1,100] - 开始获取流量风险资产数据: 测试非凡设备1
2025-08-26 15:38:58.243 [async-task-pool15] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,352] - 开始拉取流量风险资产数据，参数：FlowRiskAssetsParam(accessToken=null, riskType=all, startTime=2025-08-25 15:36:57, endTime=2025-08-26 15:36:58, page=1, pageSize=30, deviceConfigId=4)
2025-08-26 15:38:58.245 [async-task-pool18] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,352] - 开始拉取流量风险资产数据，参数：FlowRiskAssetsParam(accessToken=null, riskType=all, startTime=2025-08-25 15:36:57, endTime=2025-08-26 15:36:58, page=1, pageSize=30, deviceConfigId=1)
2025-08-26 15:38:58.247 [async-task-pool15] INFO  c.r.f.a.d.FlowRiskAssetsParam - [getRequestBase,49] - 非凡API流量风险资产请求参数: https://***************:23000/v2/flow-risk-assets?access_token=lst2BAzxJTQL0eSZ&risk_type=all&start_date=2025-08-25%2015%3A36%3A57&end_date=2025-08-26%2015%3A36%3A58&page=1&page_size=30
2025-08-26 15:38:58.248 [async-task-pool18] INFO  c.r.f.a.d.FlowRiskAssetsParam - [getRequestBase,49] - 非凡API流量风险资产请求参数: https://***************:23000/v2/flow-risk-assets?access_token=lst2BAzxJTQL0eSZ&risk_type=all&start_date=2025-08-25%2015%3A36%3A57&end_date=2025-08-26%2015%3A36%3A58&page=1&page_size=30
2025-08-26 15:38:58.277 [async-task-pool9] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,987] - 主机入侵攻击总数: 0
2025-08-26 15:38:58.278 [async-task-pool9] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1010] - 第1页获取0条数据
2025-08-26 15:38:58.283 [async-task-pool187] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,987] - 主机入侵攻击总数: 0
2025-08-26 15:38:58.286 [async-task-pool9] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1014] - 当前页无数据，停止获取
2025-08-26 15:38:58.286 [async-task-pool187] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1010] - 第1页获取0条数据
2025-08-26 15:38:58.286 [async-task-pool9] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1083] - 拉取主机入侵攻击数据成功，但没有有效数据
2025-08-26 15:38:58.295 [async-task-pool187] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1014] - 当前页无数据，停止获取
2025-08-26 15:38:58.297 [async-task-pool187] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1083] - 拉取主机入侵攻击数据成功，但没有有效数据
2025-08-26 15:38:58.309 [async-task-pool173] INFO  c.r.f.c.FFSafeRequestComponent - [execRequest,88] - 非凡请求响应: [{"id": 1058, "block_ip": "*************", "location": ["美国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-22 00:58:57", "release_time": "2025-08-29 01:00:59", "remarks": ""}, {"id": 1057, "block_ip": "************", "location": ["法国"], "action": "阻断", "conditions": ["外网可疑访问"], "type": "流量检测", "create_time": "2025-08-21 13:55:52", "release_time": "2025-08-28 13:56:53", "remarks": ""}, {"id": 1056, "block_ip": "***********", "location": ["印度"], "action": "阻断", "conditions": ["外网恶意攻击"], "type": "流量检测", "create_time": "2025-08-20 12:44:55", "release_time": "2025-08-27 12:46:57", "remarks": ""}]

2025-08-26 15:38:58.332 [async-task-pool15] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,458] - 流量风险资产总数: 6
2025-08-26 15:38:58.338 [async-task-pool15] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,481] - 第1页获取6条数据
2025-08-26 15:38:58.340 [async-task-pool15] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,488] - 当前页数据量(6)少于页大小(30)，已获取完所有数据
2025-08-26 15:38:58.371 [async-task-pool18] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,458] - 流量风险资产总数: 6
2025-08-26 15:38:58.379 [Thread-8] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 1秒147毫秒
2025-08-26 15:38:58.379 [async-task-pool18] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,481] - 第1页获取6条数据
2025-08-26 15:38:58.380 [async-task-pool18] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,488] - 当前页数据量(6)少于页大小(30)，已获取完所有数据
2025-08-26 15:38:58.393 [Thread-6] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 1秒160毫秒
2025-08-26 15:38:58.409 [taskScheduler-31] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 308毫秒
2025-08-26 15:38:58.412 [async-task-pool15] INFO  c.r.m.c.c.FfsafeClientService - [processBatch,662] - 批量更新6条流量风险资产数据
2025-08-26 15:38:58.427 [async-task-pool15] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,551] - 拉取流量风险资产数据完成，耗时184ms，共1页，共6条数据，处理结果: true
2025-08-26 15:38:58.468 [async-task-pool18] INFO  c.r.m.c.c.FfsafeClientService - [processBatch,662] - 批量更新6条流量风险资产数据
2025-08-26 15:38:58.478 [async-task-pool18] INFO  c.r.m.c.c.FfsafeClientService - [pullFlowRiskAssets,551] - 拉取流量风险资产数据完成，耗时233ms，共1页，共6条数据，处理结果: true
2025-08-26 15:38:58.521 [Thread-7] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 284毫秒
