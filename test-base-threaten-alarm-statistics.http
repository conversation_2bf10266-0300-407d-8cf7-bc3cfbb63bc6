### 测试 BaseThreatenAlarmStatistics 新增接口
### 基于你提供的查询参数格式
### 使用 aq-service 环境 (端口30000)

### 1. 测试告警等级分组查询
GET {{baseUrl}}/api/aq-service/threaten-alarm-statistics/getGroupByLevel?currentPage=1&pageSize=5&startTime=2025-07-07%2000:00:00&endTime=2025-07-14%2023:59:59&srcIp=***************&limitStartTime=2025-07-14%2010:39:25&limitEndTime=2025-07-14%2016:14:34&deviceId=1906552700959592448
Authorization: {{Authorization}}
Jnpf-Origin: {{Jnpf-Origin}}
Content-Type: application/json

### 2. 测试攻击者分组查询
GET {{baseUrl}}/api/aq-service/threaten-alarm-statistics/getGroupByAttack?currentPage=1&pageSize=5&startTime=2025-07-07%2000:00:00&endTime=2025-07-14%2023:59:59&srcIp=***************&limitStartTime=2025-07-14%2010:39:25&limitEndTime=2025-07-14%2016:14:34&deviceId=1906552700959592448
Authorization: {{Authorization}}
Jnpf-Origin: {{Jnpf-Origin}}
Content-Type: application/json

### 3. 测试受害者分组查询
GET {{baseUrl}}/api/aq-service/threaten-alarm-statistics/getGroupByVictim?currentPage=1&pageSize=5&startTime=2025-07-07%2000:00:00&endTime=2025-07-14%2023:59:59&srcIp=***************&limitStartTime=2025-07-14%2010:39:25&limitEndTime=2025-07-14%2016:14:34&deviceId=1906552700959592448
Authorization: {{Authorization}}
Jnpf-Origin: {{Jnpf-Origin}}
Content-Type: application/json

### 4. 测试现有的告警名称分组查询（对比参考）
GET {{baseUrl}}/api/aq-service/threaten-alarm-statistics/getGroupByName?currentPage=1&pageSize=5&startTime=2025-07-07%2000:00:00&endTime=2025-07-14%2023:59:59&srcIp=***************&limitStartTime=2025-07-14%2010:39:25&limitEndTime=2025-07-14%2016:14:34&deviceId=1906552700959592448
Authorization: {{Authorization}}
Jnpf-Origin: {{Jnpf-Origin}}
Content-Type: application/json

### 5. 测试现有的告警列表查询接口（对比参考）
POST {{baseUrl}}/api/dbcp/BaseThreatenAlarm/getList
Authorization: {{Authorization}}
Jnpf-Origin: {{Jnpf-Origin}}
Accept: application/json
Content-Type: application/json

{
    "superQueryJson": "",
    "currentPage": 1,
    "pageSize": 10,
    "sort": "desc",
    "sidx": "",
    "deviceId": "1906552700959592448",
    "startTime": "2025-07-07 00:00:00",
    "endTime": "2025-07-14 23:59:59",
    "limitStartTime": "2025-07-14 10:39:25",
    "limitEndTime": "2025-07-14 16:14:34",
    "srcIp": "***************",
    "dataType": 0
}


### 6. 测试攻击者IP报告列表查询
GET {{baseUrl}}/api/aq-service/threaten-alarm-statistics/selectAttackIpReportList?attackIp=***************&startTime=2025-07-08 00:00:00&endTime=2025-07-15 23:59:59&limitStartTime=2025-07-14 10:39:25&limitEndTime=2025-07-15 15:04:42&deviceId=1906552700959592448
Authorization: {{Authorization}}
Jnpf-Origin: {{Jnpf-Origin}}
Content-Type: application/json 

### 7. 测试受害者IP报告列表查询
GET {{baseUrl}}/api/aq-service/threaten-alarm-statistics/selectVictimIpReportList?victimIp=**************&startTime=2025-07-08 00:00:00&endTime=2025-07-15 23:59:59&limitStartTime=2025-07-14 08:00:05&limitEndTime=2025-07-15 15:04:20&deviceId=1906552700959592448
Authorization: {{Authorization}}
Jnpf-Origin: {{Jnpf-Origin}}
Content-Type: application/json


### 8. 测试威胁告警时间轴列表查询
GET {{baseUrl}}/api/aq-service/threaten-alarm-statistics/selectTimeAxisList?attackIp=***************&startTime=2025-07-08 00:00:00&endTime=2025-07-15 23:59:59&currentPage=1&pageSize=10&limitStartTime=2025-07-14 10:39:25&limitEndTime=2025-07-15 15:04:42&deviceId=1906552700959592448
Authorization: {{Authorization}}
Jnpf-Origin: {{Jnpf-Origin}}
Content-Type: application/json


### 9. 测试攻击次数中的受害者
GET {{baseUrl}}/api/aq-service/threaten-alarm-statistics/selectAttackIpVictimStatReport?attackIp=************&startTime=2025-07-10 00:00:00&endTime=2025-07-17 23:59:59&limitStartTime=2025-07-14 08:29:03&limitEndTime=2025-07-17 12:54:20&deviceId=1906552700959592448
Authorization: {{Authorization}}
Jnpf-Origin: {{Jnpf-Origin}}
Content-Type: application/json


###  10. 测试受害次数中遭遇的攻击者
GET {{baseUrl}}/api/aq-service/threaten-alarm-statistics/selectVictimIpAttackStatReport?victimIp=**************&startTime=2025-07-10 00:00:00&endTime=2025-07-17 23:59:59&limitStartTime=2025-07-14 08:00:05&limitEndTime=2025-07-17 12:58:05&deviceId=1906552700959592448
Authorization: {{Authorization}}
Jnpf-Origin: {{Jnpf-Origin}}
Content-Type: application/json


###  11. 测试受害者主动访问服务器
GET {{baseUrl}}/api/aq-service/threaten-alarm-statistics/selectOutConnectReportList?victimIp=************&startTime=2025-07-10 00:00:00&endTime=2025-07-17 23:59:59&limitStartTime=2025-07-14 08:29:03&limitEndTime=2025-07-17 12:54:20&deviceId=1906552700959592448
Authorization: {{Authorization}}
Jnpf-Origin: {{Jnpf-Origin}}
Content-Type: application/json



### 12. 测试攻击者访问受害者
POST {{baseUrl}}/api/aq-service/attackAlarm/getList
Authorization: {{Authorization}}
Jnpf-Origin: {{Jnpf-Origin}}
Accept: application/json
Content-Type: application/json

{
	"superQueryJson": "",
	"currentPage": 1,
	"pageSize": 20,
	"sort": "desc",
	"sidx": "",
	"location": [],
	"deviceId": "1906552700959592448",
	"dataType": 0,
    "blockStatus":2,
	"menuId": "715231407785494405",
	"moduleId": "712601124267472261"
}



###  13. 测试攻击者目标IP数统计接口
GET {{baseUrl}}/api/aq-service/flow-detail-statistics/getDipDetails?attackIp=**************&startTime=2025-07-21%2008%3A00%3A07&updateTime=2025-07-21%2010%3A30%3A37&deviceId=1906552700959592448
Authorization: {{Authorization}}
Jnpf-Origin: {{Jnpf-Origin}}
Content-Type: application/json



###  14. 测试命中规则统计接口
GET {{baseUrl}}/api/aq-service/flow-detail-statistics/getThreatenNameDetails?attackIp=**************&startTime=2025-07-21%2008%3A00%3A07&updateTime=2025-07-21%2010%3A37%3A42&deviceId=1906552700959592448
Authorization: {{Authorization}}
Jnpf-Origin: {{Jnpf-Origin}}
Content-Type: application/json



### 查询首页快捷入口配置列表
# group: 九紫平台-首页
# @name 查询首页快捷入口配置列表
GET {{baseUrl}}/system/entrance/list?pageNum=1&pageSize=10&params%5BbeginTime%5D=2025-07-15&params%5BendTime%5D=2025-07-17
Authorization: {{Authorization}}
Accept: application/json
Content-Type: application/json


### 获取首页通知列表
# group: 九紫平台-首页
# @name 获取首页通知列表
GET {{baseUrl}}/system/notice/homeList?pageNum=1&pageSize=100&includeRead=false
Authorization: {{Authorization}}
Accept: application/json
Content-Type: application/json


### 查询首页快捷入口配置列表
# group: 九紫平台-首页
# @name 查询首页快捷入口配置列表
GET {{baseUrl}}/system/entrance/category
Authorization: {{Authorization}}
Accept: application/json
Content-Type: application/json


### 检查快捷入口是否存在    
# group: 九紫平台-首页
# @name 检查快捷入口是否存在
GET {{baseUrl}}/system/entrance/checkExists
Authorization: {{Authorization}}
Accept: application/json
Content-Type: application/json


### 服务中台-监测结果
# group: 服务中台-监测结果
# @name 导出监测结果
POST {{baseUrl}}/safe/server/importJtTemplate
Authorization: {{Authorization}}
Jnpf-Origin: {{Jnpf-Origin}}
Content-Type: application/json
Accept: application/octet-stream; charset=binary

{
    "currentPage": 1,
    "pageSize": 20,
    "sort": "desc",
    "sidx": "",
    "dataType": 0
}


### 服务中台-监测结果
# group: 服务中台-监测结果
# @name 导出监测结果
POST {{baseUrl}}/safe/safety/importJtTemplate
Authorization: {{Authorization}}
Jnpf-Origin: {{Jnpf-Origin}}
Content-Type: application/json
Accept: application/octet-stream; charset=binary

{
    "currentPage": 1,
    "pageSize": 20,
    "sort": "desc",
    "sidx": "",
    "dataType": 0
}


###   
# group: 服务中台-监测结果
# @name 
GET {{baseUrl}}/hostscan/tasksummary/listWithDetails?pageNum=1&pageSize=10000&taskType=2
Authorization: {{Authorization}}
Accept: application/json
Content-Type: application/json


###



### 批量生成漏扫报表  
# group: 漏洞扫描
# @name 批量生成漏扫报表
POST {{baseUrl}}/hostscan/tasksummary/createBatchTaskReport
Authorization: {{Authorization}}
Jnpf-Origin: {{Jnpf-Origin}}
Accept: application/json
Content-Type: application/json

{
    "ids": [760],
    "taskType": 2
}



###   
# group: 服务中台-监测结果
# @name 
GET {{baseUrl}}/ffsafe/scanreportrecord/list?pageNum=1&pageSize=100&reportType=2
Authorization: {{Authorization}}
Accept: application/json
Content-Type: application/json


### 查询主机入侵攻击列表  
# group: 九紫平台-查询主机入侵攻击列表
# @name 
GET {{baseUrl}}/ffsafe/hostIntrusionAttack/list?pageNum=1&pageSize=100&sip=**************&deviceConfigId=1&dip=**************
Authorization: {{Authorization}}
Accept: application/json
Content-Type: application/json



### 查询主机入侵攻击详情  
# group: 九紫平台-查询主机入侵攻击详情
# @name 
GET {{baseUrl}}/ffsafe/hostIntrusionAttack/45
Authorization: {{Authorization}}
Accept: application/json
Content-Type: application/json


### 删除主机入侵攻击  
# group: 九紫平台-删除主机入侵攻击
# @name 
DELETE {{baseUrl}}/ffsafe/hostIntrusionAttack/44
Authorization: {{Authorization}}
Accept: application/json
Content-Type: application/json


### 查询主机入侵攻击列表  
# group: 九紫平台-查询主机入侵攻击列表
# @name 
GET {{baseUrl}}/ffsafe/hostIntrusionAttack/statistics?sip=**************&deviceConfigId=1&dip=**************
Authorization: {{Authorization}}
Accept: application/json
Content-Type: application/json


### 服务中台-监测结果
# group: 服务中台-监测结果
# @name 处置监测结果
POST {{baseUrl}}/ffsafe/hostIntrusionAttack/handle
Authorization: {{Authorization}}
Jnpf-Origin: {{Jnpf-Origin}}
Content-Type: application/json

{
    "id": 45,
    "handleState": 1,
    "handleDesc": "12344445"
}



###
# 业务系统新增
# group: 业务系统新增
# @name 业务系统新增
POST {{baseUrl}}/ffsafe/hostIntrusionAttack/batchHandle
Content-Type: application/json
Accept: application/json
Authorization: {{Authorization}}

[
  {
    "id": "1905874241842159617",
    "handleState": "1",
    "handleDesc": "12234"
  }
]


### 查询主机入侵攻击详情  
# group: 九紫平台-查询主机入侵攻击详情
# @name 
GET {{baseUrl}}/ffsafe/hostIntrusionAttack/detail/45
Authorization: {{Authorization}}
Accept: application/json
Content-Type: application/json





