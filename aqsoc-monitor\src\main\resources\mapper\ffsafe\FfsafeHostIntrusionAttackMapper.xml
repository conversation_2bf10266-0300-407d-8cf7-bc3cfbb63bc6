<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.ffsafe.api.mapper.FfsafeHostIntrusionAttackMapper">
    
    <resultMap type="FfsafeHostIntrusionAttack" id="FfsafeHostIntrusionAttackResult">
        <result property="id" column="id" />
        <result property="ffId" column="ff_id" />
        <result property="sip" column="sip" />
        <result property="dip" column="dip" />
        <result property="dipName" column="dip_name" />
        <result property="alertName" column="alert_name" />
        <result property="startTime" column="start_time" />
        <result property="updateTime" column="update_time" />
        <result property="handleState" column="handle_state" />
        <result property="handleDesc" column="handle_desc" />
        <result property="disposer" column="disposer" />
        <result property="deviceConfigId" column="device_config_id" />
        <result property="createTime" column="create_time" />
        <result property="createBy" column="create_by" />
        <result property="updateBy" column="update_by" />
    </resultMap>

    <sql id="selectFfsafeHostIntrusionAttackVo">
        select id, ff_id, sip, dip, dip_name, alert_name, start_time, update_time,
               handle_state, handle_desc, disposer, device_config_id, create_time, create_by, update_by
        from ffsafe_host_intrusion_attack
    </sql>

    <select id="selectFfsafeHostIntrusionAttackList" parameterType="FfsafeHostIntrusionAttack" resultMap="FfsafeHostIntrusionAttackResult">
        <include refid="selectFfsafeHostIntrusionAttackVo"/>
        <where>  
            <if test="ffId != null "> and ff_id = #{ffId}</if>
            <if test="sip != null  and sip != ''"> and sip = #{sip}</if>
            <if test="dip != null  and dip != ''"> and dip = #{dip}</if>
            <if test="dipName != null  and dipName != ''"> and dip_name like concat('%', #{dipName}, '%')</if>
            <if test="alertName != null  and alertName != ''"> and alert_name like concat('%', #{alertName}, '%')</if>
            <if test="startTime != null "> and start_time = #{startTime}</if>
            <if test="updateTime != null "> and update_time = #{updateTime}</if>
            <if test="handleState != null "> and handle_state = #{handleState}</if>
            <if test="handleDesc != null  and handleDesc != ''"> and handle_desc = #{handleDesc}</if>
            <if test="disposer != null  and disposer != ''"> and disposer = #{disposer}</if>
            <if test="deviceConfigId != null "> and device_config_id = #{deviceConfigId}</if>
        </where>
        order by update_time desc
    </select>
    
    <select id="selectFfsafeHostIntrusionAttackById" parameterType="Long" resultMap="FfsafeHostIntrusionAttackResult">
        <include refid="selectFfsafeHostIntrusionAttackVo"/>
        where id = #{id}
    </select>

    <select id="selectByFfIdAndDeviceConfigId" resultMap="FfsafeHostIntrusionAttackResult">
        <include refid="selectFfsafeHostIntrusionAttackVo"/>
        where ff_id = #{ffId} and device_config_id = #{deviceConfigId}
    </select>

    <select id="selectByMultipleFields" parameterType="java.util.List" resultMap="FfsafeHostIntrusionAttackResult">
        <include refid="selectFfsafeHostIntrusionAttackVo"/>
        where (ff_id, device_config_id) in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            (#{item.ffId}, #{item.deviceConfigId})
        </foreach>
    </select>
        
    <insert id="insertFfsafeHostIntrusionAttack" parameterType="FfsafeHostIntrusionAttack" useGeneratedKeys="true" keyProperty="id">
        insert into ffsafe_host_intrusion_attack
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="ffId != null">ff_id,</if>
            <if test="sip != null and sip != ''">sip,</if>
            <if test="dip != null and dip != ''">dip,</if>
            <if test="dipName != null">dip_name,</if>
            <if test="alertName != null and alertName != ''">alert_name,</if>
            <if test="startTime != null">start_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="handleState != null">handle_state,</if>
            <if test="handleDesc != null">handle_desc,</if>
            <if test="disposer != null">disposer,</if>
            <if test="deviceConfigId != null">device_config_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="ffId != null">#{ffId},</if>
            <if test="sip != null and sip != ''">#{sip},</if>
            <if test="dip != null and dip != ''">#{dip},</if>
            <if test="dipName != null">#{dipName},</if>
            <if test="alertName != null and alertName != ''">#{alertName},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="handleState != null">#{handleState},</if>
            <if test="handleDesc != null">#{handleDesc},</if>
            <if test="disposer != null">#{disposer},</if>
            <if test="deviceConfigId != null">#{deviceConfigId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
         </trim>
    </insert>

    <update id="updateFfsafeHostIntrusionAttack" parameterType="FfsafeHostIntrusionAttack">
        update ffsafe_host_intrusion_attack
        <trim prefix="SET" suffixOverrides=",">
            <if test="ffId != null">ff_id = #{ffId},</if>
            <if test="sip != null and sip != ''">sip = #{sip},</if>
            <if test="dip != null and dip != ''">dip = #{dip},</if>
            <if test="dipName != null">dip_name = #{dipName},</if>
            <if test="alertName != null and alertName != ''">alert_name = #{alertName},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="handleState != null">handle_state = #{handleState},</if>
            <if test="handleDesc != null">handle_desc = #{handleDesc},</if>
            <if test="disposer != null">disposer = #{disposer},</if>
            <if test="deviceConfigId != null">device_config_id = #{deviceConfigId},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteFfsafeHostIntrusionAttackById" parameterType="Long">
        delete from ffsafe_host_intrusion_attack where id = #{id}
    </delete>

    <delete id="deleteFfsafeHostIntrusionAttackByIds" parameterType="String">
        delete from ffsafe_host_intrusion_attack where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <insert id="batchInsertFfsafeHostIntrusionAttack" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
        insert into ffsafe_host_intrusion_attack (ff_id, sip, dip, dip_name, alert_name, start_time, update_time, 
                                                  handle_state, handle_desc, disposer, device_config_id, create_by, update_by)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.ffId}, #{item.sip}, #{item.dip}, #{item.dipName}, #{item.alertName}, #{item.startTime}, 
             #{item.updateTime}, #{item.handleState}, #{item.handleDesc}, #{item.disposer}, #{item.deviceConfigId}, 
             #{item.createBy}, #{item.updateBy})
        </foreach>
    </insert>

    <!-- 批量更新方法：在ExecutorType.BATCH模式下，MyBatis会为每个item创建单独的PreparedStatement -->
    <!-- 因此不需要使用separator，每个update语句会被单独执行 -->
    <update id="batchUpdateFfsafeHostIntrusionAttack" parameterType="FfsafeHostIntrusionAttack">
        update ffsafe_host_intrusion_attack
        <set>
            sip = #{sip},
            dip = #{dip},
            dip_name = #{dipName},
            alert_name = #{alertName},
            start_time = #{startTime},
            update_time = #{updateTime},
            handle_state = #{handleState},
            handle_desc = #{handleDesc},
            disposer = #{disposer},
            device_config_id = #{deviceConfigId},
            update_by = #{updateBy}
        </set>
        where id = #{id}
    </update>

    <update id="batchUpdateHandleState">
        update ffsafe_host_intrusion_attack
        set handle_state = #{handleState},
            handle_desc = #{handleDesc},
            disposer = #{disposer}
        where id in
        <foreach collection="eventIds" item="eventId" open="(" separator="," close=")">
            #{eventId}
        </foreach>
    </update>

    <!-- 根据查询DTO查询主机入侵攻击事件列表 -->
    <select id="selectFfsafeHostIntrusionAttackListByQuery" parameterType="FfsafeHostIntrusionAttackQueryDto" resultMap="FfsafeHostIntrusionAttackResult">
        <include refid="selectFfsafeHostIntrusionAttackVo"/>
        <where>
            <if test="sip != null and sip != ''"> and sip = #{sip}</if>
            <if test="dip != null and dip != ''"> and dip = #{dip}</if>
            <if test="dipName != null and dipName != ''"> and dip_name like concat('%', #{dipName}, '%')</if>
            <if test="alertName != null and alertName != ''"> and alert_name like concat('%', #{alertName}, '%')</if>
            <if test="handleState != null"> and handle_state = #{handleState}</if>
            <if test="deviceConfigId != null"> and device_config_id = #{deviceConfigId}</if>
            <if test="disposer != null and disposer != ''"> and disposer like concat('%', #{disposer}, '%')</if>
            <if test="ffId != null"> and ff_id = #{ffId}</if>
            <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
                and update_time &gt;= #{params.beginTime}
            </if>
            <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
                and update_time &lt;= #{params.endTime}
            </if>
        </where>
        order by update_time desc
    </select>

    <!-- 统计满足查询条件的记录总数 -->
    <select id="selectAttackTypeStatistics" resultType="java.util.Map">
        SELECT
            COUNT(*) as total
        FROM ffsafe_host_intrusion_attack
        <where>
            <if test="sip != null and sip != ''"> and sip = #{sip}</if>
            <if test="dip != null and dip != ''"> and dip = #{dip}</if>
            <if test="dipName != null and dipName != ''"> and dip_name like concat('%', #{dipName}, '%')</if>
            <if test="alertName != null and alertName != ''"> and alert_name like concat('%', #{alertName}, '%')</if>
            <if test="handleState != null"> and handle_state = #{handleState}</if>
            <if test="deviceConfigId != null"> and device_config_id = #{deviceConfigId}</if>
            <if test="disposer != null and disposer != ''"> and disposer like concat('%', #{disposer}, '%')</if>
            <if test="ffId != null"> and ff_id = #{ffId}</if>
            <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
                and update_time &gt;= #{params.beginTime}
            </if>
            <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
                and update_time &lt;= #{params.endTime}
            </if>
        </where>
    </select>

    <!-- 根据攻击ID查询攻击详情（包含详情表数据） -->
    <select id="selectAttackDetailByAttackId" parameterType="Long" resultType="java.util.Map">
        SELECT
            a.id,
            a.ff_id,
            a.sip,
            a.dip,
            a.dip_name,
            a.alert_name,
            a.start_time,
            a.update_time,
            a.handle_state,
            a.handle_desc,
            a.disposer,
            a.device_config_id,
            a.create_time,
            a.create_by,
            a.update_by,
            d.detail_type,
            d.detail_data
        FROM ffsafe_host_intrusion_attack a
        LEFT JOIN ffsafe_host_intrusion_attack_detail d ON a.id = d.attack_id
        WHERE a.id = #{attackId}
    </select>

</mapper>
