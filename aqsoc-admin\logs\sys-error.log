2025-08-26 15:32:33.273 [async-task-pool45] ERROR c.a.d.f.s.<PERSON>atFilter - [internalAfterStatementExecute,504] - slow sql 4624 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-26 15:32:06"]
2025-08-26 15:32:35.231 [async-task-pool55] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4035 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-26 15:32:03"]
2025-08-26 15:32:49.184 [async-task-pool55] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 13949 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-26 15:32:03"]
2025-08-26 15:32:52.250 [async-task-pool14] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2518 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-26 15:32:23"]
2025-08-26 15:32:52.580 [async-task-pool51] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2434 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-26 15:32:40"]
2025-08-26 15:32:54.296 [async-task-pool75] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3811 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-26 15:32:22"]
2025-08-26 15:32:55.233 [async-task-pool14] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2980 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-26 15:32:23"]
2025-08-26 15:33:08.503 [async-task-pool14] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 13234 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-26 15:32:23"]
2025-08-26 15:33:13.379 [async-task-pool132] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2165 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-26 15:32:40"]
2025-08-26 15:33:13.467 [async-task-pool135] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2640 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-26 15:32:23"]
2025-08-26 15:33:14.604 [async-task-pool90] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3109 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-26 15:32:22"]
2025-08-26 15:33:16.192 [async-task-pool135] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2708 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-26 15:32:23"]
2025-08-26 15:33:43.111 [async-task-pool135] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 26915 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-26 15:32:23"]
2025-08-26 15:33:45.580 [async-task-pool165] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1843 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["***************","2025-08-26 14:58:22","2025-08-26 15:32:50"]
2025-08-26 15:33:47.845 [async-task-pool165] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2261 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["***************","2025-08-26 14:58:22","2025-08-26 15:32:50"]
2025-08-26 15:33:48.271 [async-task-pool148] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3733 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-26 15:33:40"]
2025-08-26 15:33:48.363 [async-task-pool155] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4627 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-26 15:33:37"]
2025-08-26 15:33:50.472 [async-task-pool147] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 5484 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-26 15:33:33"]
2025-08-26 15:33:53.590 [async-task-pool155] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 5220 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-26 15:33:37"]
2025-08-26 15:34:17.555 [async-task-pool165] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 29705 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-26 14:58:22","2025-08-26 15:32:50"]
2025-08-26 15:34:23.118 [async-task-pool155] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 29525 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-26 15:33:37"]
2025-08-26 15:34:27.314 [async-task-pool181] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1511 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["***************","2025-08-26 14:58:22","2025-08-26 15:34:10"]
2025-08-26 15:34:28.975 [async-task-pool181] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1655 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["***************","2025-08-26 14:58:22","2025-08-26 15:34:10"]
2025-08-26 15:34:29.612 [async-task-pool25] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3241 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-26 15:34:10"]
2025-08-26 15:34:29.669 [async-task-pool10] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3864 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-26 15:34:06"]
2025-08-26 15:34:31.264 [async-task-pool27] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4402 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-26 15:34:05"]
2025-08-26 15:34:33.235 [async-task-pool10] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3563 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-26 15:34:06"]
2025-08-26 15:34:54.429 [async-task-pool181] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 25450 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-26 14:58:22","2025-08-26 15:34:10"]
2025-08-26 15:35:03.899 [async-task-pool10] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 30658 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-26 15:34:06"]
2025-08-26 15:35:06.109 [async-task-pool71] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1539 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["***************","2025-08-26 14:58:22","2025-08-26 15:34:10"]
2025-08-26 15:35:08.090 [async-task-pool71] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1977 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["***************","2025-08-26 14:58:22","2025-08-26 15:34:10"]
2025-08-26 15:35:08.123 [async-task-pool31] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2990 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-26 15:34:40"]
2025-08-26 15:35:08.420 [async-task-pool78] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3851 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-26 15:34:37"]
2025-08-26 15:35:09.942 [async-task-pool50] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4305 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-26 15:34:38"]
2025-08-26 15:35:12.375 [async-task-pool78] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3952 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-26 15:34:37"]
2025-08-26 15:35:33.078 [async-task-pool71] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 24985 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-26 14:58:22","2025-08-26 15:34:10"]
2025-08-26 15:35:38.845 [async-task-pool78] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 26464 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-26 15:34:37"]
2025-08-26 15:35:40.740 [async-task-pool129] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1287 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["***************","2025-08-26 14:58:22","2025-08-26 15:34:10"]
2025-08-26 15:35:42.586 [async-task-pool129] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1843 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["***************","2025-08-26 14:58:22","2025-08-26 15:34:10"]
2025-08-26 15:35:43.031 [async-task-pool134] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3070 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-26 15:35:10"]
2025-08-26 15:35:43.047 [async-task-pool137] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3593 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-26 15:34:57"]
2025-08-26 15:35:45.702 [async-task-pool136] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 5318 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-26 15:35:08"]
2025-08-26 15:35:48.625 [async-task-pool137] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 5576 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-26 15:34:57"]
2025-08-26 15:36:10.744 [async-task-pool129] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 28153 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-26 14:58:22","2025-08-26 15:34:10"]
2025-08-26 15:36:17.477 [async-task-pool137] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 28848 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-26 15:34:57"]
2025-08-26 15:36:19.291 [async-task-pool147] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1378 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["***************","2025-08-26 14:58:22","2025-08-26 15:34:10"]
2025-08-26 15:36:20.940 [async-task-pool147] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1639 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["***************","2025-08-26 14:58:22","2025-08-26 15:34:10"]
2025-08-26 15:36:21.153 [async-task-pool189] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2736 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-26 15:36:10"]
2025-08-26 15:36:21.678 [async-task-pool169] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3762 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-26 15:36:12"]
2025-08-26 15:36:24.869 [async-task-pool175] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 5929 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-26 15:36:08"]
2025-08-26 15:36:29.008 [async-task-pool169] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 7325 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-26 15:36:12"]
2025-08-26 15:36:56.141 [async-task-pool147] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 35197 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-26 14:58:22","2025-08-26 15:34:10"]
2025-08-26 15:37:03.742 [async-task-pool169] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 34731 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-26 15:36:12"]
2025-08-26 15:37:05.116 [async-task-pool34] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 9237 millis. update tbl_device_config
         SET host_intrusion_last_time = ? 
        where id = ?["2025-08-26 15:34:55",1]
2025-08-26 15:37:05.125 [async-task-pool26] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 9243 millis. update tbl_device_config
         SET host_intrusion_last_time = ? 
        where id = ?["2025-08-26 15:34:55",4]
2025-08-26 15:37:05.139 [async-task-pool194] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 7000 millis. update ffsafe_interface_config
         SET data_last_time = ? 
        where
        interface_path = ?["2025-08-26 15:36:58","/v2/flow-bypass-filtering-log"]
2025-08-26 15:37:05.140 [async-task-pool37] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 7002 millis. update ffsafe_interface_config
         SET data_last_time = ? 
        where
        interface_path = ?["2025-08-26 15:36:58","/v2/flow-bypass-filtering-log"]
2025-08-26 15:37:05.140 [async-task-pool12] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 7825 millis. update ffsafe_flow_risk_assets
             set risk_type =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                risk_info =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                engine_name =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                start_time =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                update_time =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                device_config_id =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end 
            where id in
             (  
                ?
             , 
                ?
             , 
                ?
             , 
                ?
             , 
                ?
             )[5,"sensitive_info",35,"sensitive_info",31,"sensitive_info",29,"sensitive_info",33,"sensitive_info",5,"['36**************24', '36**************27', '36**************1X', '43**************35', '36*****...",35,"['36**************12']",31,"['36**************24', '36**************27', '36**************1X', '43**************35', '36*****...",29,"['35**************38', '35**************13', '35**************58', '41**************38', '35*****...",33,"['36**************24', '36**************27', '43**************35', '36**************33', '36*****...",5,"********",35,"********",31,"********",29,"********",33,"********",5,"2024-10-30 14:22:32",35,"2025-08-25 17:52:00",31,"2024-10-30 13:58:43",29,"2024-10-29 14:41:00",33,"2024-10-29 14:40:33",5,"2025-08-26 15:17:20",35,"2025-08-25 17:52:01",31,"2025-08-25 17:04:59",29,"2025-08-25 16:27:04",33,"2025-08-25 16:08:30",5,1,35,1,31,1,29,1,33,1,5,35,31,29,33]
2025-08-26 15:37:05.157 [async-task-pool4] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 7827 millis. update ffsafe_flow_risk_assets
             set risk_type =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                risk_info =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                engine_name =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                start_time =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                update_time =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                device_config_id =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end 
            where id in
             (  
                ?
             , 
                ?
             , 
                ?
             , 
                ?
             , 
                ?
             )[5,"sensitive_info",35,"sensitive_info",31,"sensitive_info",29,"sensitive_info",33,"sensitive_info",5,"['36**************24', '36**************27', '36**************1X', '43**************35', '36*****...",35,"['36**************12']",31,"['36**************24', '36**************27', '36**************1X', '43**************35', '36*****...",29,"['35**************38', '35**************13', '35**************58', '41**************38', '35*****...",33,"['36**************24', '36**************27', '43**************35', '36**************33', '36*****...",5,"********",35,"********",31,"********",29,"********",33,"********",5,"2024-10-30 14:22:32",35,"2025-08-25 17:52:00",31,"2024-10-30 13:58:43",29,"2024-10-29 14:41:00",33,"2024-10-29 14:40:33",5,"2025-08-26 15:17:20",35,"2025-08-25 17:52:01",31,"2025-08-25 17:04:59",29,"2025-08-25 16:27:04",33,"2025-08-25 16:08:30",5,4,35,4,31,4,29,4,33,4,5,35,31,29,33]
2025-08-26 15:37:05.387 [async-task-pool28] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3648 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            
            
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time,
            
            device_config_id ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            
            
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ? )["**************",43819,"*******",53,"dns","[1621949] 挖矿病毒事件（请求恶意域名：thegov.win）","网络攻击/后门利用/木马通信","W3siY29udGVudF9oZXgiOiAiNzQ2ODY1Njc2Zjc2MmU3NzY5NmUiLCAiY29udGVudF9zdHJpbmciOiAidGhlZ292LndpbiJ9XQ==","af1eda48-6135-11ef-8e67-000c29677ec8","********",1621949,4,"请求","DuABAAABAAAAAAAAA2RlYgZ0aGVnb3YDd2luAAABAAE=","[]","2025-08-26 15:36:52",1]
2025-08-26 15:37:05.400 [async-task-pool16] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3668 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            
            
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time,
            
            device_config_id ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            
            
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ? )["**************",43819,"*******",53,"dns","[1621949] 挖矿病毒事件（请求恶意域名：thegov.win）","网络攻击/后门利用/木马通信","W3siY29udGVudF9oZXgiOiAiNzQ2ODY1Njc2Zjc2MmU3NzY5NmUiLCAiY29udGVudF9zdHJpbmciOiAidGhlZ292LndpbiJ9XQ==","af1eda48-6135-11ef-8e67-000c29677ec8","********",1621949,4,"请求","DuABAAABAAAAAAAAA2RlYgZ0aGVnb3YDd2luAAABAAE=","[]","2025-08-26 15:36:52",4]
2025-08-26 15:37:05.519 [pool-6-thread-1] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1359 millis. UPDATE tbl_attack_alarm SET
        risk_level = CASE id
          
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
         
        ELSE risk_level
        END,
        location = CASE id
          
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
         
        ELSE location
        END,
        victim_ip_nums = CASE id
          
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
         
        ELSE victim_ip_nums
        END,
        attack_type_nums = CASE id
          
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
         
        ELSE attack_type_nums
        END,
        attack_nums = CASE id
          
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
         
        ELSE attack_nums
        END,
        update_time = CASE id
          
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
         
        ELSE update_time
        END[1176,3,1174,2,1175,3,1177,1,1240,3,1188,1,1237,3,1227,1,1224,1,1197,2,1179,1,1180,1,1233,1,1181,1,1182,1,1176,"局域网",1174,"局域网",1175,"局域网",1177,"局域网",1240,"局域网",1188,"局域网",1237,"局域网",1227,"局域网",1224,"局域网",1197,"局域网",1179,"局域网",1180,"局域网",1233,"局域网",1181,"局域网",1182,"局域网",1176,2,1174,2,1175,9,1177,1,1240,26,1188,2,1237,242,1227,1,1224,1,1197,2,1179,1,1180,1,1233,1,1181,2,1182,2,1176,2,1174,5,1175,9,1177,1,1240,1071,1188,2,1237,60,1227,1,1224,3,1197,5,1179,1,1180,1,1233,1,1181,1,1182,1,1176,588497,1174,24595,1175,48387,1177,2029,1240,186364,1188,400,1237,5885,1227,1009,1224,440,1197,428,1179,1354,1180,886,1233,44,1181,1091,1182,1090,1176,"2025-08-26 15:36:12",1174,"2025-08-26 15:36:10",1175,"2025-08-26 15:36:08",1177,"2025-08-26 15:35:31",1240,"2025-08-26 15:34:10",1188,"2025-08-26 15:33:16",1237,"2025-08-26 15:32:50",1227,"2025-08-26 15:31:58",1224,"2025-08-26 15:31:50",1197,"2025-08-26 15:30:35",1179,"2025-08-26 15:29:54",1180,"2025-08-26 15:29:41",1233,"2025-08-26 15:28:37",1181,"2025-08-26 15:27:58",1182,"2025-08-26 15:27:20"]
2025-08-26 15:37:07.531 [async-task-pool33] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1802 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["***************","2025-08-26 14:58:22","2025-08-26 15:34:10"]
2025-08-26 15:37:09.739 [async-task-pool29] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3111 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-26 15:36:40"]
2025-08-26 15:37:10.038 [async-task-pool33] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2503 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["***************","2025-08-26 14:58:22","2025-08-26 15:34:10"]
2025-08-26 15:37:10.576 [async-task-pool19] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4850 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-26 15:36:41"]
2025-08-26 15:37:12.106 [async-task-pool25] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 5095 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-26 15:36:38"]
2025-08-26 15:37:18.331 [async-task-pool19] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 7750 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-26 15:36:41"]
2025-08-26 15:37:32.915 [async-task-pool33] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 22873 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-26 14:58:22","2025-08-26 15:34:10"]
2025-08-26 15:37:48.821 [async-task-pool19] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 30487 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-26 15:36:41"]
2025-08-26 15:37:50.848 [async-task-pool103] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1277 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["***************","2025-08-26 14:58:22","2025-08-26 15:34:10"]
2025-08-26 15:37:52.105 [async-task-pool50] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2037 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-26 15:37:40"]
2025-08-26 15:37:52.494 [async-task-pool103] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1643 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["***************","2025-08-26 14:58:22","2025-08-26 15:34:10"]
2025-08-26 15:37:53.495 [async-task-pool104] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3215 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-26 15:37:34"]
2025-08-26 15:38:10.048 [async-task-pool103] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 17551 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-26 14:58:22","2025-08-26 15:34:10"]
2025-08-26 15:38:29.512 [async-task-pool78] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 17173 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-26 14:58:22","2025-08-26 15:34:10"]
2025-08-26 15:38:32.487 [async-task-pool144] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1873 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["***************","2025-08-26 14:58:22","2025-08-26 15:34:10"]
2025-08-26 15:38:39.449 [async-task-pool144] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 6954 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-26 14:58:22","2025-08-26 15:34:10"]
2025-08-26 15:38:42.971 [async-task-pool146] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1392 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["***************","2025-08-26 14:58:22","2025-08-26 15:34:10"]
2025-08-26 15:38:44.411 [async-task-pool146] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1437 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["***************","2025-08-26 14:58:22","2025-08-26 15:34:10"]
2025-08-26 15:38:53.219 [async-task-pool146] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 8802 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-26 14:58:22","2025-08-26 15:34:10"]
2025-08-26 15:39:10.887 [async-task-pool7] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 11938 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-26 14:58:22","2025-08-26 15:34:10"]
2025-08-26 15:39:27.126 [async-task-pool34] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 14217 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-26 14:58:22","2025-08-26 15:34:10"]
2025-08-26 15:39:43.517 [async-task-pool41] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 13737 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-26 14:58:22","2025-08-26 15:34:10"]
2025-08-26 15:39:46.354 [async-task-pool60] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1127 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["***************","2025-08-26 14:58:22","2025-08-26 15:34:10"]
2025-08-26 15:39:57.816 [async-task-pool60] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 11301 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-26 14:58:22","2025-08-26 15:34:10"]
2025-08-26 15:40:00.598 [async-task-pool108] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1240 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["***************","2025-08-26 14:58:22","2025-08-26 15:34:10"]
2025-08-26 15:40:12.056 [async-task-pool108] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 11142 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-26 14:58:22","2025-08-26 15:34:10"]
2025-08-26 15:40:30.368 [async-task-pool114] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 16447 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-26 14:58:22","2025-08-26 15:34:10"]
2025-08-26 15:40:32.029 [async-task-pool171] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1100 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["***************","2025-08-26 14:58:22","2025-08-26 15:34:10"]
2025-08-26 15:40:33.116 [async-task-pool171] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1054 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["***************","2025-08-26 14:58:22","2025-08-26 15:34:10"]
2025-08-26 15:40:46.914 [async-task-pool171] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 13794 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-26 14:58:22","2025-08-26 15:34:10"]
2025-08-26 15:40:48.666 [async-task-pool149] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1314 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["***************","2025-08-26 14:58:22","2025-08-26 15:34:10"]
2025-08-26 15:40:50.349 [async-task-pool149] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1679 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["***************","2025-08-26 14:58:22","2025-08-26 15:34:10"]
2025-08-26 15:40:50.989 [async-task-pool192] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3186 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-26 15:40:10"]
2025-08-26 15:40:51.368 [async-task-pool182] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4015 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-26 15:40:11"]
2025-08-26 15:40:52.944 [async-task-pool137] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4644 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-26 15:40:05"]
2025-08-26 15:40:55.489 [async-task-pool182] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4117 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-26 15:40:11"]
2025-08-26 15:41:00.705 [async-task-pool149] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 10350 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-26 14:58:22","2025-08-26 15:34:10"]
2025-08-26 15:41:14.605 [async-task-pool182] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 19072 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-26 15:40:11"]
2025-08-26 15:41:16.962 [async-task-pool23] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1148 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["***************","2025-08-26 14:58:22","2025-08-26 15:34:10"]
2025-08-26 15:41:28.584 [async-task-pool23] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 11352 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-26 14:58:22","2025-08-26 15:34:10"]
2025-08-26 15:41:30.175 [async-task-pool30] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1167 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["***************","2025-08-26 14:58:22","2025-08-26 15:34:10"]
2025-08-26 15:41:31.543 [async-task-pool30] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1363 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["***************","2025-08-26 14:58:22","2025-08-26 15:34:10"]
2025-08-26 15:41:31.939 [async-task-pool63] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2519 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-26 15:41:10"]
2025-08-26 15:41:31.963 [async-task-pool27] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2961 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-26 15:41:23"]
2025-08-26 15:41:33.288 [async-task-pool35] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3453 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-26 15:41:15"]
2025-08-26 15:41:34.986 [async-task-pool27] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3017 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-26 15:41:23"]
2025-08-26 15:41:45.094 [async-task-pool30] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 13543 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-26 14:58:22","2025-08-26 15:34:10"]
2025-08-26 15:41:49.921 [async-task-pool27] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 14930 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-26 15:41:23"]
2025-08-26 15:41:53.940 [async-task-pool44] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1726 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-26 15:41:10"]
2025-08-26 15:42:07.948 [async-task-pool79] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 14373 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-26 14:58:22","2025-08-26 15:34:10"]
2025-08-26 15:42:10.289 [async-task-pool109] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1007 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["***************","2025-08-26 14:58:22","2025-08-26 15:34:10"]
2025-08-26 15:42:22.866 [async-task-pool109] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 12574 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-26 14:58:22","2025-08-26 15:34:10"]
2025-08-26 15:42:25.502 [async-task-pool134] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1072 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["***************","2025-08-26 14:58:22","2025-08-26 15:34:10"]
2025-08-26 15:42:39.012 [async-task-pool134] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 13507 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-26 14:58:22","2025-08-26 15:34:10"]
2025-08-26 15:42:43.072 [async-task-pool152] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1659 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["***************","2025-08-26 14:58:22","2025-08-26 15:34:10"]
2025-08-26 15:42:43.383 [async-task-pool114] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2471 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-26 15:42:10"]
2025-08-26 15:42:43.653 [async-task-pool126] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3216 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-26 15:42:16"]
2025-08-26 15:42:45.038 [async-task-pool112] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3847 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-26 15:42:21"]
2025-08-26 15:42:47.226 [async-task-pool126] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3565 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-26 15:42:16"]
2025-08-26 15:42:56.554 [async-task-pool152] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 13469 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-26 14:58:22","2025-08-26 15:34:10"]
2025-08-26 15:43:04.069 [async-task-pool126] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 16838 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-26 15:42:16"]
2025-08-26 15:43:18.163 [async-task-pool6] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 11649 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-26 14:58:22","2025-08-26 15:34:10"]
2025-08-26 15:43:31.579 [async-task-pool42] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 11231 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-26 14:58:22","2025-08-26 15:34:10"]
2025-08-26 15:43:44.755 [async-task-pool34] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 11217 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-26 14:58:22","2025-08-26 15:34:10"]
2025-08-26 15:43:46.455 [async-task-pool33] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1218 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["***************","2025-08-26 14:58:22","2025-08-26 15:34:10"]
2025-08-26 15:43:48.086 [async-task-pool33] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1627 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["***************","2025-08-26 14:58:22","2025-08-26 15:34:10"]
2025-08-26 15:43:48.615 [async-task-pool61] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2843 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-26 15:43:10"]
2025-08-26 15:43:48.652 [async-task-pool68] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3428 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-26 15:43:07"]
